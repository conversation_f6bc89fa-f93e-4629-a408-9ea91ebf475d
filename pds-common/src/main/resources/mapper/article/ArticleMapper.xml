<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.ArticleMapper">

    <resultMap id="ArticleResult" type="org.biosino.lf.pds.article.domain.Article">
        <id property="id" column="id"/>
        <result property="customId" column="custom_id"/>
        <result property="pmid" column="pmid"/>
        <result property="pmcId" column="pmc_id"/>
        <result property="doi" column="doi"/>
        <result property="pubStatus" column="pub_status"/>
        <result property="language" column="language"/>
        <result property="vernacularTitle" column="vernacular_title"/>
        <result property="title" column="title"/>
        <result property="publishedYear" column="published_year"/>
        <result property="publishedMonth" column="published_month"/>
        <result property="publishedDay" column="published_day"/>
        <result property="journalId" column="journal_id"/>
        <result property="journalName" column="journal_name"/>
        <result property="issnPrint" column="issn_print"/>
        <result property="issnElectronic" column="issn_electronic"/>
        <result property="year" column="year"/>
        <result property="volume" column="volume"/>
        <result property="issue" column="issue"/>
        <result property="page" column="page"/>
        <result property="articleAbstract" column="abstract"/>
        <result property="otherAbstract" column="other_abstract"/>
        <result property="copyright" column="copyright"/>
        <result property="hitNum" column="hit_num"/>
        <result property="download" column="download"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="source" column="source"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="author" column="author"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="affiliation" column="affiliation"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="authorInfo" column="author_info"
                typeHandler="org.biosino.lf.pds.article.config.AuthorInfoListTypeHandler"/>
        <result property="keywords" column="keywords"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="otherDate" column="other_date"
                typeHandler="org.biosino.lf.pds.article.config.PubMedPubDateListTypeHandler"/>
        <result property="existPdf" column="exist_pdf"/>
    </resultMap>

    <sql id="selectArticleVo">
        select a.id,
               a.custom_id,
               a.pmid,
               a.pmc_id,
               a.doi,
               a.source,
               a.pub_status,
               a.language,
               a.vernacular_title,
               a.title,
               a.published_year,
               a.published_month,
               a.published_day,
               a.other_date,
               a.journal_id,
               j.title as journal_name,
               j.issn_print      as issn_print,
               j.issn_electronic as issn_electronic,
               a.year,
               a.volume,
               a.issue,
               a.page,
               a.author,
               a.affiliation,
               a.author_info,
               a.keywords,
               a.abstract,
               a.other_abstract,
               a.copyright,
               a.hit_num,
               a.download,
               a.create_time,
               a.update_time
        from tb_dds_article a
                 left join tb_dds_journal j on a.journal_id = j.id
    </sql>

    <update id="updateJournalIdBatch">
        update tb_dds_article
        set journal_id = #{targetJournalId}
        where journal_id in
        <foreach collection="sourceJournalIds" item="sourceId" open="(" separator="," close=")">
            #{sourceId}
        </foreach>
    </update>

    <select id="selectArticleList" parameterType="org.biosino.lf.pds.article.dto.ArticleQueryDTO"
            resultMap="ArticleResult">
        <include refid="selectArticleVo"/>
        <where>
            <if test="id != null">
                a.id = #{id}
            </if>
            <if test="pmid != null">
                a.pmid = #{pmid}
            </if>
            <if test="pmcId != null">
                and a.pmc_id = #{pmcId}
            </if>
            <if test="customId != null">
                and a.custom_id = #{customId}
            </if>
            <if test="doi != null and doi != ''">
                and a.doi = #{doi}
            </if>
            <if test="title != null and title != ''">
                and a.title ILIKE concat('%', #{title}, '%')
            </if>
            <if test="volume != null and volume != ''">
                and a.volume = #{volume}
            </if>
            <if test="journalName != null and journalName != ''">
                and j.title ILIKE concat('%', #{journalName}, '%')
            </if>
            <if test="yearStart != null">
                and a.published_year <![CDATA[>=]]> #{yearStart}
            </if>
            <if test="yearEnd != null">
                and a.published_year <![CDATA[<=]]> #{yearEnd}
            </if>
            <if test="monthStart != null">
                and a.published_month <![CDATA[>=]]> #{monthStart}
            </if>
            <if test="monthEnd != null">
                and a.published_month <![CDATA[<=]]> #{monthEnd}
            </if>
            <if test="dayStart != null">
                and a.published_day <![CDATA[>=]]> #{dayStart}
            </if>
            <if test="dayEnd != null">
                and a.published_day <![CDATA[<=]]> #{dayEnd}
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="selectArticleById" parameterType="Long" resultMap="ArticleResult">
        <include refid="selectArticleVo"/>
        <where>
            a.id = #{id}
        </where>
    </select>

    <select id="searchArticleNotInTaskPaper" resultType="org.biosino.lf.pds.article.domain.Article">
        SELECT
        a.id, a.pmid, a.pmc_id, a.doi
        FROM
        tb_dds_article a
        <if test="wosQuartile != null and wosQuartile != ''">
            INNER JOIN tb_dds_journal j ON (j.id = a.journal_id)
        </if>
        <where>
            <if test="wosQuartile != null and wosQuartile != ''">
                AND j.wos_quartile = #{wosQuartile}
            </if>
            AND a.create_time &lt;= CAST(#{dateBefore} AS TIMESTAMP WITH TIME ZONE)
            AND a.id NOT IN (SELECT t.doc_id FROM tb_dds_task_paper t GROUP BY t.doc_id)
            AND a.id NOT IN (SELECT f.doc_id FROM tb_dds_file f WHERE f.TYPE = 'PDF')
        </where>
        ORDER BY a.create_time DESC, a.id DESC
        LIMIT #{limitNum}
    </select>

    <!-- 获取最受欢迎的期刊（按文献点击量汇总排序） -->
    <select id="selectPopularJournals" resultType="org.biosino.lf.pds.article.dto.JournalSummaryDTO">
        SELECT j.id, j.title, t.hitNum, zs.large_category AS category
        FROM (
                 SELECT a.journal_id, SUM(a.hit_num) AS hitNum
                 FROM tb_dds_article a
                 GROUP BY a.journal_id
                 ORDER BY SUM(a.hit_num) DESC
                     LIMIT 5
             ) t
                 INNER JOIN tb_dds_journal j ON t.journal_id = j.id
                 LEFT JOIN tb_dds_zky_section zs ON j.id = zs.journal_id
        ORDER BY t.hitNum DESC;
    </select>
</mapper>
