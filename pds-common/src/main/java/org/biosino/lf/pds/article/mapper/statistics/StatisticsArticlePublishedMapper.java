package org.biosino.lf.pds.article.mapper.statistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.biosino.lf.pds.article.domain.statistics.StatisticsArticlePublished;

import java.util.List;

@Mapper
public interface StatisticsArticlePublishedMapper extends BaseMapper<StatisticsArticlePublished> {
    @Delete("DELETE FROM statistics_article_published WHERE year = #{year} AND month = #{month}")
    void deleteByYearAndMonth(int year, int month);
    
    @Select("SELECT * FROM statistics_article_published ORDER BY year DESC, month DESC LIMIT 7")
    List<StatisticsArticlePublished> selectLast7Records();
    
    @Select("SELECT * FROM statistics_article_published WHERE year = #{year} AND month = #{month} LIMIT 1")
    StatisticsArticlePublished selectByYearAndMonth(int year, int month);
}
