package org.biosino.lf.pds.article.mapper.statistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.biosino.lf.pds.article.domain.statistics.StatisticsArticleDownload;

import java.util.List;

@Mapper
public interface StatisticsArticleDownloadMapper extends BaseMapper<StatisticsArticleDownload> {
    
    /**
     * 删除指定年月的统计数据
     */
    @Delete("DELETE FROM statistics_article_download WHERE year = #{year} AND month = #{month}")
    void deleteByYearAndMonth(int year, int month);
    
    /**
     * 查询指定年月的统计数据
     */
    @Select("SELECT * FROM statistics_article_download WHERE year = #{year} AND month = #{month} LIMIT 1")
    StatisticsArticleDownload selectByYearAndMonth(int year, int month);
    
    /**
     * 查询最近7个月的统计数据
     */
    @Select("SELECT * FROM statistics_article_download ORDER BY year DESC, month DESC LIMIT 7")
    List<StatisticsArticleDownload> selectLast7Records();
}