package org.biosino.lf.pds.article.mapper.statistics;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.biosino.lf.pds.article.domain.statistics.StatisticsArticle;
import org.biosino.lf.pds.article.mapper.CommonMapper;

import java.util.List;

@Mapper
public interface StatisticsArticleMapper extends CommonMapper<StatisticsArticle> {
    @Delete("DELETE FROM statistics_article WHERE year = #{year} AND month = #{month}")
    void deleteByYearAndMonth(int year, int month);
    
    @Select("SELECT * FROM statistics_article ORDER BY year DESC, month DESC LIMIT 7")
    List<StatisticsArticle> selectLast7Records();
    
    @Select("SELECT * FROM statistics_article WHERE year = #{year} AND month = #{month} LIMIT 1")
    StatisticsArticle selectByYearAndMonth(int year, int month);
}
