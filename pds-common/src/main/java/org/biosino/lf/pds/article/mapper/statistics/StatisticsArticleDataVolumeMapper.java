package org.biosino.lf.pds.article.mapper.statistics;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.biosino.lf.pds.article.domain.statistics.StatisticsArticleDataVolume;
import org.biosino.lf.pds.article.mapper.CommonMapper;

import java.util.List;

@Mapper
public interface StatisticsArticleDataVolumeMapper extends CommonMapper<StatisticsArticleDataVolume> {
    @Delete("DELETE FROM statistics_article_data_volume WHERE year = #{year} AND month = #{month}")
    void deleteByYearAndMonth(int year, int month);
    
    @Select("SELECT * FROM statistics_article_data_volume ORDER BY year DESC, month DESC LIMIT 7")
    List<StatisticsArticleDataVolume> selectLast7Records();
    
    @Select("SELECT * FROM statistics_article_data_volume WHERE year = #{year} AND month = #{month} LIMIT 1")
    StatisticsArticleDataVolume selectByYearAndMonth(int year, int month);
}
