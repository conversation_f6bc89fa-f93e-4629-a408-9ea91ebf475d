package org.biosino.lf.pds.article.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.ArticleAttachmentUpload;
import org.biosino.lf.pds.article.dto.ArticleAttachmentUploadQueryDTO;

import java.util.List;

/**
 * 文章附件上传信息 Mapper 接口
 */
@Mapper
public interface ArticleAttachmentUploadMapper extends CommonMapper<ArticleAttachmentUpload> {

    /**
     * 查询上传列表（关联用户和文件表）
     *
     * @param queryDTO 查询条件
     * @return 上传列表
     */
    List<ArticleAttachmentUpload> selectUploadList(ArticleAttachmentUploadQueryDTO queryDTO);
    
    /**
     * 批量更新文章附件上传表中的doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateDocIdBatch(@Param("targetDocId") Long targetDocId, @Param("sourceDocId") Long sourceDocId);
}
