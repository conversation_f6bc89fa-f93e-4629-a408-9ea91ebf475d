package org.biosino.lf.pds.article.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.biosino.lf.pds.article.domain.TbDdsFile;

import java.util.Date;

/**
 * <p>
 * 存放文件metadata元数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface TbDdsFileMapper extends CommonMapper<TbDdsFile> {

    /**
     * 批量更新任务论文表中的doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateDocIdBatch(@Param("targetDocId") Long targetDocId, @Param("sourceDocId") Long sourceDocId);

    /**
     * 统计PDF文件总数据量（docId不为空且type是PDF）
     * 
     * @param endTime 截止时间
     * @return 总数据量（字节）
     */
    @Select("SELECT COALESCE(SUM(file_size), 0) FROM tb_dds_file WHERE doc_id IS NOT NULL AND type = 'PDF' AND create_time <= #{endTime}")
    Long sumPdfFileSizeByEndTime(@Param("endTime") Date endTime);

    /**
     * 统计PDF文件数据量（docId不为空且type是PDF）
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据量（字节）
     */
    @Select("SELECT COALESCE(SUM(file_size), 0) FROM tb_dds_file WHERE doc_id IS NOT NULL AND type = 'PDF' AND create_time >= #{startTime} AND create_time <= #{endTime}")
    Long sumPdfFileSizeByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

}
