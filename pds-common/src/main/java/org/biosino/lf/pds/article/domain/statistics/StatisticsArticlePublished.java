package org.biosino.lf.pds.article.domain.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.common.annotation.Excel;
import org.biosino.lf.pds.common.annotation.Excel.ColumnType;

/**
 * <AUTHOR>
 * @date 2025/8/29
 */
@Data
@TableName(value = "statistics_article_published", autoResultMap = true)
public class StatisticsArticlePublished {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 年
     */
    @TableField("year")
    @Excel(name = "年份", cellType = ColumnType.NUMERIC)
    private Integer year;

    /**
     * 月
     */
    @TableField("month")
    @Excel(name = "月份", cellType = ColumnType.NUMERIC)
    private Integer month;

    /**
     * 统计本月所有publish的数量
     */
    @TableField("total")
    @Excel(name = "发布文献总数", cellType = ColumnType.NUMERIC)
    private Long total = 0L;

    /**
     * 统计本月新增publish的数量
     */
    @TableField("total_growth")
    @Excel(name = "新增发布文献数", cellType = ColumnType.NUMERIC)
    private Long totalGrowth = 0L;
}
