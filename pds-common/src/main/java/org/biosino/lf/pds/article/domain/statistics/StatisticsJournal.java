package org.biosino.lf.pds.article.domain.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.common.annotation.Excel;
import org.biosino.lf.pds.common.annotation.Excel.ColumnType;

/**
 * 期刊统计实体类
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "statistics_journal", autoResultMap = true)
public class StatisticsJournal {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 年
     */
    @TableField("year")
    @Excel(name = "年份", cellType = ColumnType.NUMERIC)
    private Integer year;

    /**
     * 月
     */
    @TableField("month")
    @Excel(name = "月份", cellType = ColumnType.NUMERIC)
    private Integer month;

    /**
     * 统计本月期刊总数
     */
    @TableField("total")
    @Excel(name = "期刊总数", cellType = ColumnType.NUMERIC)
    private Long total = 0L;

    /**
     * 统计本月新增期刊数量
     */
    @TableField("total_growth")
    @Excel(name = "新增期刊数", cellType = ColumnType.NUMERIC)
    private Long totalGrowth = 0L;
}
