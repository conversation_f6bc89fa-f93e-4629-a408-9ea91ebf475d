package org.biosino.lf.pds.common.enums;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 文件上传状态枚举
 * 状态（0-待审核，1-已接受，-1已驳回）
 *
 * <AUTHOR>
 */
@Getter
public enum ArticleAttachmentUploadStatusEnum {
    VERIFY_WAITING(0, "待审核"),
    NORMAL(1, "已接受"),
    VERIFY_FAILED(-1, "已驳回");;
    private final Integer code;
    private final String text;

    ArticleAttachmentUploadStatusEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    public static Optional<ArticleAttachmentUploadStatusEnum> findByCode(Integer code) {
        if (code == null) {
            return Optional.empty();
        }
        for (ArticleAttachmentUploadStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }

    public static Map<Integer, String> toMap() {
        final Map<Integer, String> map = new LinkedHashMap<>();
        for (ArticleAttachmentUploadStatusEnum value : values()) {
            map.put(value.getCode(), value.getText());
        }
        return map;
    }

}
