package org.biosino.lf.pds.common.enums;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

@Getter
public enum ArticleConrrectionStatusEnum {

    VERIFY_WAITING("待审核", 0), NORMAL("已接受", 1), VERIFY_FAILED("已关闭", -1);

    private final String description;
    private final int code;

    ArticleConrrectionStatusEnum(String description, int code) {
        this.description = description;
        this.code = code;
    }

    public static Map<String, String> toMap() {
        Map<String, String> map = new LinkedHashMap<>();
        for (ArticleConrrectionStatusEnum e : values()) {
            map.put(e.name(), e.getDescription());
        }
        return map;
    }


}
