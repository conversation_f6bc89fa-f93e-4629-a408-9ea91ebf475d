package org.biosino.lf.pds.article.domain.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.common.annotation.Excel;

/**
 * PDF文件数据量统计实体
 *
 * <AUTHOR> Assistant
 * @date 2025/9/2
 */
@Data
@TableName(value = "statistics_article_data_volume", autoResultMap = true)
public class StatisticsArticleDataVolume {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 年
     */
    @TableField("year")
    @Excel(name = "年份", cellType = Excel.ColumnType.NUMERIC)
    private Integer year;

    /**
     * 月
     */
    @TableField("month")
    @Excel(name = "月份", cellType = Excel.ColumnType.NUMERIC)
    private Integer month;

    /**
     * 统计本月文献PDF数据磁盘总量
     */
    @TableField("total")
    private Long total = 0L;

    @TableField(exist = false)
    @Excel(name = "数据总量(GB)")
    private String readableTotal;

    /**
     * 统计新增文献PDF数据磁盘总量
     */
    @TableField("total_growth")
    private Long totalGrowth = 0L;

    @Excel(name = "本月新增数据总量(GB)")
    private String readableTotalGrowth;
}
