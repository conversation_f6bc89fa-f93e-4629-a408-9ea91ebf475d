package org.biosino.lf.pds.system.domain.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.common.annotation.Excel;
import org.biosino.lf.pds.common.annotation.Excel.ColumnType;

/**
 * 用户统计实体
 *
 * <AUTHOR>
 * @date 2025/9/3
 */
@Data
@TableName(value = "statistics_user", autoResultMap = true)
public class StatisticsUser {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 年
     */
    @TableField("year")
    @Excel(name = "年份", cellType = ColumnType.NUMERIC)
    private Integer year;

    /**
     * 月
     */
    @TableField("month")
    @Excel(name = "月份", cellType = ColumnType.NUMERIC)
    private Integer month;

    /**
     * 统计截止到当月的用户总数
     */
    @TableField("total")
    @Excel(name = "用户总数", cellType = ColumnType.NUMERIC)
    private Long total = 0L;

    /**
     * 统计当月新增用户数量
     */
    @TableField("total_growth")
    @Excel(name = "新增用户数", cellType = ColumnType.NUMERIC)
    private Long totalGrowth = 0L;
}
