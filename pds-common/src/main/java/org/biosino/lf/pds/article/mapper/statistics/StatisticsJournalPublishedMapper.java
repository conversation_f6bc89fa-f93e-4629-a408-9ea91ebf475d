package org.biosino.lf.pds.article.mapper.statistics;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.biosino.lf.pds.article.domain.statistics.StatisticsJournalPublished;
import org.biosino.lf.pds.article.mapper.CommonMapper;

import java.util.List;

@Mapper
public interface StatisticsJournalPublishedMapper extends CommonMapper<StatisticsJournalPublished> {
    @Delete("DELETE FROM statistics_journal_published WHERE year = #{year} AND month = #{month}")
    void deleteByYearAndMonth(int year, int month);
    
    @Select("SELECT * FROM statistics_journal_published ORDER BY year DESC, month DESC LIMIT 7")
    List<StatisticsJournalPublished> selectLast7Records();
    
    @Select("SELECT * FROM statistics_journal_published WHERE year = #{year} AND month = #{month} LIMIT 1")
    StatisticsJournalPublished selectByYearAndMonth(int year, int month);
}
