package org.biosino.lf.pds.article.domain.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.common.annotation.Excel;
import org.biosino.lf.pds.common.annotation.Excel.ColumnType;

/**
 * 文献下载量统计实体
 *
 * <AUTHOR>
 * @date 2025/8/30
 */
@Data
@TableName(value = "statistics_article_download", autoResultMap = true)
public class StatisticsArticleDownload {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 年
     */
    @TableField("year")
    @Excel(name = "年份", cellType = ColumnType.NUMERIC)
    private Integer year;

    /**
     * 月
     */
    @TableField("month")
    @Excel(name = "月份", cellType = ColumnType.NUMERIC)
    private Integer month;

    /**
     * 当月总下载量
     */
    @TableField("total")
    @Excel(name = "总下载量", cellType = ColumnType.NUMERIC)
    private Long total = 0L;
}
