package org.biosino.lf.pds.system.mapper.statistics;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.biosino.lf.pds.article.mapper.CommonMapper;
import org.biosino.lf.pds.system.domain.statistics.StatisticsUser;

import java.util.List;

@Mapper
public interface StatisticsUserMapper extends CommonMapper<StatisticsUser> {
    @Delete("DELETE FROM statistics_user WHERE year = #{year} AND month = #{month}")
    void deleteByYearAndMonth(int year, int month);
    
    @Select("SELECT * FROM statistics_user ORDER BY year DESC, month DESC LIMIT 7")
    List<StatisticsUser> selectLast7Records();
    
    @Select("SELECT * FROM statistics_user WHERE year = #{year} AND month = #{month} LIMIT 1")
    StatisticsUser selectByYearAndMonth(int year, int month);
}
