package org.biosino.lf.pds.article.custbean.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.Data;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.biosino.lf.pds.article.domain.Article;
import org.biosino.lf.pds.article.mapper.ArticleMapper;

import java.util.*;
import java.util.stream.Collectors;

import static org.biosino.lf.pds.common.utils.StringUtils.numStr;

/**
 * <AUTHOR>
 */
@Data
public class TransmitListVO {
    private String articleId;
    private String title;
    private String status;
    private List<String> author;

    private String journalName;
    private String year;
    private String publishedYear;

    private String volume;
    private String issue;
    private String page;
    private String pmid;
    private String pmcId;
    private String doi;

    private String createTime;

    private long paperId;
    private String taskId;

    private String reason;
    // 纠错类型
    private String correctionType;
    // 纠错信息
    private String correctionContent;

    private String fileName;
    private Integer uploadId;

//    private Long id;
//    private String transmitId;
//    private String source;
//    private String description;


    public static TransmitListVO init(final Date createTime, final Long docId, final Map<Long, Article> docIdArticleMap, final Map<Long, String> journalTitleMap) {
        final TransmitListVO vo = new TransmitListVO();
        vo.setCreateTime(DateFormatUtils.format(createTime, DatePattern.NORM_DATE_PATTERN));

        final Article article = docIdArticleMap.get(docId);
        if (article != null) {
            final Long pmid = article.getPmid();
            vo.setArticleId(docId.toString());
            vo.setTitle(article.getTitle());
            vo.setAuthor(article.getAuthor());

            final Long journalId = article.getJournalId();
            if (journalId != null) {
                vo.setJournalName(journalTitleMap.get(journalId));
            }
            vo.setPublishedYear(numStr(article.getPublishedYear()));
            vo.setYear(numStr(article.getYear()));
            vo.setVolume(article.getVolume());
            vo.setIssue(article.getIssue());
            vo.setPage(article.getPage());
            vo.setPmid(numStr(pmid));
            vo.setPmcId(numStr(article.getPmcId()));
            vo.setDoi(article.getDoi());
        }
        return vo;
    }

    public static Set<Long> findArticleSearchIds(final Set<Long> docIds, final String articleTitle, final ArticleMapper articleMapper) {
        if (CollUtil.isEmpty(docIds)) {
            return new HashSet<>();
        }
        // 根据PMID/PMCID/DOI/标题筛选，四项筛选添加之间的逻辑为或关系
        final LambdaQueryWrapper<Article> articleWrapper = Wrappers.lambdaQuery(Article.class)
                .in(Article::getId, docIds).and(wrapper -> {
                    wrapper.apply("title ILIKE {0}", "%" + articleTitle + "%")
                            .or()
                            .apply("doi ILIKE {0}", "%" + articleTitle + "%");
                    final Long pmidOrPmcId = parseLong(articleTitle);
                    if (pmidOrPmcId != null) {
                        wrapper.or()
                                .eq(Article::getPmid, pmidOrPmcId)
                                .or()
                                .eq(Article::getPmcId, pmidOrPmcId);
                    }
                }).select(Article::getId);

        return articleMapper.selectList(articleWrapper).stream().map(Article::getId).collect(Collectors.toSet());
    }

    private static Long parseLong(String val) {
        try {
            if (val.toUpperCase().startsWith("PMC")) {
                return Long.parseLong(val.substring(3));
            } else {
                return Long.parseLong(val);
            }
        } catch (Exception e) {
            return null;
        }
    }
}
