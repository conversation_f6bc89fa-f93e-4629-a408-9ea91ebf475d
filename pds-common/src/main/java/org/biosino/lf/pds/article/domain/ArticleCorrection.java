package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文献纠错信息实体类
 */
@Data
@TableName(value = "tb_dds_article_correction", autoResultMap = true)
public class ArticleCorrection implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 文献ID
     */
    @TableField("doc_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long docId;

    /**
     * 文献标题
     */
    @TableField("title")
    private String title;

    /**
     * 纠错类型
     */
    @TableField("correction_type")
    private String correctionType;

    /**
     * 纠错内容
     */
    @TableField("content")
    private String content;

    /**
     * 提交人ID
     */
    @TableField("creator")
    private Long creator;


    /**
     * 非数据库字段
     */
    @TableField(exist = false)
    private String creatorName;

    /**
     * 驳回原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 提交时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 处理人ID
     */
    @TableField("auditor")
    private Long auditor;

    /**
     * 非数据库字段
     */
    @TableField(exist = false)
    private String auditorName;

    /**
     * 处理时间
     */
    @TableField("audit_time")
    private Date auditTime;

    /**
     * 状态
     * @see org.biosino.lf.pds.common.enums.ArticleConrrectionStatusEnum
     */
    @TableField("status")
    private Integer status;

}
