package org.biosino.lf.pds.article.domain.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.common.annotation.Excel;

/**
 * 期刊发表统计实体
 *
 * <AUTHOR> Assistant
 * @date 2025/9/2
 */
@Data
@TableName(value = "statistics_journal_published", autoResultMap = true)
public class StatisticsJournalPublished {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 年
     */
    @TableField("year")
    @Excel(name = "年份", cellType = Excel.ColumnType.NUMERIC)
    private Integer year;

    /**
     * 月
     */
    @TableField("month")
    @Excel(name = "月份", cellType = Excel.ColumnType.NUMERIC)
    private Integer month;

    /**
     * 统计本月发布期刊总数
     */
    @TableField("total")
    @Excel(name = "发布期刊总数", cellType = Excel.ColumnType.NUMERIC)
    private Long total = 0L;

    /**
     * 统计本月新增发布期刊数
     */
    @Excel(name = "新增发布期刊数", cellType = Excel.ColumnType.NUMERIC)
    @TableField("total_growth")
    private Long totalGrowth = 0L;
}
