package org.biosino.lf.pds.common.core.mail;

import lombok.Getter;

@Getter
public enum MailTemplateEnum {

    TASK_PDF_DOWNLOAD("PDS文献传递任务结果下载地址", "pds_task_pdf_down.ftlh"),
    SITE_ERROR("PDS文献传递节点异常报告", "site_error.ftlh"),
    PLOSP_PORTAL_UPLOAD_PDF("PLOSP全文上传通知", "plosp_portal_upload_pdf.ftlh"),
    PLOSP_ADMIN_AUDIT_UPLOAD_PDF("PLOSP全文上传审核通过通知", "plosp_admin_audit_upload_pdf.ftlh"),
    PLOSP_ADMIN_AUDIT_CORRECTION("PLOSP文献纠错审核通过通知", "plosp_admin_audit_correction.ftlh"),
    ;

    private final String operatorName;
    private final String templateName;

    MailTemplateEnum(String operatorName, String templateName) {
        this.operatorName = operatorName;
        this.templateName = templateName;
    }


}
