package org.biosino.lf.pds.article.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.biosino.lf.pds.article.domain.Article;
import org.biosino.lf.pds.article.dto.ArticleQueryDTO;
import org.biosino.lf.pds.article.dto.JournalSummaryDTO;

import java.util.Date;
import java.util.List;

/**
 * 文章信息表 Mapper 接口
 */
@Mapper
public interface ArticleMapper extends CommonMapper<Article> {

    /**
     * 查询文献列表
     *
     * @param queryDTO 查询参数
     * @return 文献列表
     */
    List<Article> selectArticleList(ArticleQueryDTO queryDTO);

    /**
     * 获取文献详情
     *
     * @param id 文献ID
     * @return 文献详情
     */
    Article selectArticleById(Long id);

    List<Article> searchArticleNotInTaskPaper(@Param("dateBefore") String dateBefore, @Param("wosQuartile") String wosQuartile, @Param("limitNum") int limitNum);

    /**
     * 批量更新文献的期刊ID（用于期刊合并）
     *
     * @param targetJournalId  目标期刊ID
     * @param sourceJournalIds 源期刊ID列表
     */
    void updateJournalIdBatch(Long targetJournalId, List<Long> sourceJournalIds);

    /**
     * 获取总下载量
     *
     * @return 总下载量
     */
    @Select("select coalesce(sum(download)::bigint, 0) from tb_dds_article")
    Long getTotalDownload();

    /**
     * 获取最受欢迎的期刊（按点击量排序）
     *
     * @return 最受欢迎的期刊列表
     */
    List<JournalSummaryDTO> selectPopularJournals();

    /**
     * 统计截止到某时间的期刊总数（以该期刊第一篇入库文献的入库时间为准）
     *
     * @param endTime 截止时间
     * @return 期刊总数
     */
    @Select("""
                SELECT COUNT(*) AS new_journal_count
                FROM (
                    SELECT a.journal_id,
                           MIN(a.create_time) AS first_article_time
                    FROM tb_dds_article a
                    WHERE a.journal_id IS NOT NULL
                      
                    GROUP BY a.journal_id
                ) t 
                WHERE t.first_article_time <= #{endTime}
            """)
    Long countJournalsByArticleEndTime(@Param("endTime") Date endTime);

    /**
     * 统计某时间范围内的新增期刊数量（以该期刊第一篇入库文献的入库时间为准）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 新增期刊数量
     */
    @Select("""
                SELECT COUNT(*) AS new_journal_count
                FROM (
                    SELECT a.journal_id,
                           MIN(a.create_time) AS first_article_time
                    FROM tb_dds_article a
                    WHERE a.journal_id IS NOT NULL
                    GROUP BY a.journal_id
                ) t
                WHERE t.first_article_time >= #{startTime}
                  AND t.first_article_time <= #{endTime}
            """)
    Long countJournalsByArticleTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计截止到某发表时间的期刊总数（以该期刊第一篇入库文献的发表时间为准）
     *
     * @return 期刊总数
     */
    @Select("""
                SELECT COUNT(*) AS new_journal_count
                FROM (
                    SELECT a.journal_id,
                           MIN(a.published_year * 100 + a.published_month) AS first_pub_ym
                    FROM tb_dds_article a
                    WHERE a.journal_id IS NOT NULL
                      AND a.published_year IS NOT NULL
                      AND a.published_month IS NOT NULL
                    GROUP BY a.journal_id
                ) t
                WHERE t.first_pub_ym <= (#{endYear} * 100 + #{endMonth})
            """)
    Long countJournalsByPublishedEndTime(@Param("endYear") Integer endYear, @Param("endMonth") Integer endMonth);

    /**
     * 统计某发表时间范围内的新增期刊数量（以该期刊第一篇入库文献的发表时间为准）
     *
     * @return 新增期刊数量
     */
    @Select("""
                SELECT COUNT(*) AS new_journal_count
                FROM (
                    SELECT a.journal_id,
                           MIN(a.published_year * 100 + a.published_month) AS first_pub_ym
                    FROM tb_dds_article a
                    WHERE a.journal_id IS NOT NULL
                      AND a.published_year IS NOT NULL
                      AND a.published_month IS NOT NULL
                    GROUP BY a.journal_id
                ) t
                WHERE t.first_pub_ym = (#{year} * 100 + #{month})
            """)
    Long countJournalsByPublishedTime(@Param("year") Integer year,
                                      @Param("month") Integer month);
}
