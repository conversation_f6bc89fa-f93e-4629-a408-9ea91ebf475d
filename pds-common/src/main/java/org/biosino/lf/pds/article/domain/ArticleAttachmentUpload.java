package org.biosino.lf.pds.article.domain;

/**
 * <AUTHOR>
 * @date 2025/6/20
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.common.enums.ArticleAttachmentUploadStatusEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * 文章附件上传信息实体类
 */
@Data
@TableName(value = "tb_dds_article_attachment_upload", autoResultMap = true)
public class ArticleAttachmentUpload implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer id;

    /**
     * 文档ID
     */
    @TableField("doc_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long docId;

    /**
     * 文献标题
     */
    @TableField("title")
    private String title;

    /**
     * 附件ID
     */
    @TableField("attachment_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long attachmentId;

    /**
     * 创建者ID
     */
    @TableField("creator")
    private Long creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 审核者ID
     */
    @TableField("auditor")
    private Long auditor;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private Date auditTime;

    /**
     * 状态（0-待审核，1-已接受，-1已驳回）
     *
     * @see ArticleAttachmentUploadStatusEnum
     */
    @TableField("status")
    private Integer status;

    /**
     * 驳回原因
     */
    @TableField("reason")
    private String reason;

    @TableField("md5")
    private String md5;

    @TableField("file_name")
    private String fileName;

    @TableField(exist = false)
    private String creatorName;

    @TableField(exist = false)
    private String auditorName;

    @TableField(exist = false)
    private String attachmentName;

}
