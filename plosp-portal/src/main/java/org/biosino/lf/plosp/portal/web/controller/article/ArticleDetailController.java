package org.biosino.lf.plosp.portal.web.controller.article;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.common.core.domain.R;
import org.biosino.lf.plosp.portal.web.vo.ReferenceResult;
import org.biosino.lf.plosp.portal.web.service.ArticleDetailService;
import org.biosino.lf.plosp.portal.web.vo.ArticleDetailVO;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/detail")
public class ArticleDetailController {

    private final ArticleDetailService articleDetailService;

    /**
     * 获取文章详情
     */
    @GetMapping("/{id}")
    public R<ArticleDetailVO> getArticleDetail(@PathVariable String id) {
        ArticleDetailVO articleDetail = articleDetailService.getArticleDetail(id);
        return R.ok(articleDetail);
    }

    /**
     * 分析摘要
     */
    @PostMapping("/interpret/{docId}")
    public R<String> interpretArticle(@PathVariable Long docId) {
        String orCreateInterpretation = articleDetailService.getOrCreateInterpretation(docId);
        return R.ok(orCreateInterpretation);
    }

    /**
     * 获取文章引用信息
     */
    @GetMapping("/{docId}/references")
    public R<ReferenceResult> getReferences(
            @PathVariable Long docId,
            @RequestParam(defaultValue = "0") Integer limit) {
        ReferenceResult references = articleDetailService.getReferences(docId, limit);

        return R.ok(references);
    }
}
