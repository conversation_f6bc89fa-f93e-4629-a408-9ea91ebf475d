package org.biosino.lf.plosp.portal.es.service;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;

import com.google.common.util.concurrent.RateLimiter;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 远程向量化服务客户端
 * <AUTHOR>
 */
@Service
@Slf4j
public class RemoteEmbeddingService {

    private final RestTemplate restTemplate;

    @Value("${embedding.service.url:https://dev.biosino.org/zj-knowledge-api/text_embedding}")
    private String embeddingServiceUrl;

    @Value("${embedding.service.api-key:sk-8DjtvDqlyKwv1915krV74Oac5-mcWvEt}")
    private String apiKey;

    // 重试配置
    @Value("${embedding.service.retry.maxAttempts:3}")
    private int maxRetryAttempts;

    @Value("${embedding.service.retry.baseDelay:1000}")
    private long baseRetryDelay;

    @Value("${embedding.service.retry.maxDelay:10000}")
    private long maxRetryDelay;

    @Value("${embedding.service.timeout:30000}")
    private long requestTimeout;

    // 限流器：每秒最多n个请求
    private final RateLimiter rateLimiter;
    
    // 构造函数中初始化RateLimiter
    public RemoteEmbeddingService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
        //每秒最多n个请求
        this.rateLimiter = RateLimiter.create(10.0);
    }

    /**
     * 调用远程向量化服务（带限流机制）
     */
    public float[] embed(String text) {
        // 基本输入验证
        if (text == null || text.trim().isEmpty()) {
            log.warn("输入文本为空，抛出异常让上层处理");
            throw new IllegalArgumentException("输入文本不能为空");
        }

        // 限制文本长度，避免过长文本导致服务异常
        if (text.length() > 8000) {
            text = text.substring(0, 8000);
            log.debug("文本过长，截取前8000个字符");
        }
        Exception lastException = null;
        String threadName = Thread.currentThread().getName();

        for (int attempt = 1; attempt <= maxRetryAttempts; attempt++) {
            try {
                // 使用Guava RateLimiter进行限流，会阻塞直到获得许可
                rateLimiter.acquire();

                long startTime = System.currentTimeMillis();
                float[] result = doEmbed(text);
                long endTime = System.currentTimeMillis();

                if (log.isDebugEnabled()) {
                    log.debug("向量化成功，线程: {}, 耗时: {}ms, 文本长度: {}",
                             threadName, (endTime - startTime), text.length());
                }

                return result;

            } catch (Exception e) {
                lastException = e;

                if (attempt < maxRetryAttempts && shouldRetry(e)) {
                    // 计算重试延迟，429错误延迟更长
                    long delay = calculateRetryDelay(attempt);
                    if (e.getMessage() != null && e.getMessage().contains("429")) {
                        delay = delay * 3;
                    }

                    log.warn("向量化失败，线程: {}, 尝试: {}/{}, 将在{}ms后重试, 错误: {}",
                            threadName, attempt, maxRetryAttempts, delay, e.getMessage());

                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("重试被中断，线程: {}", threadName);
                        throw new RuntimeException("重试被中断", ie);
                    }
                } else {
                    log.error("向量化最终失败，线程: {}, 尝试: {}/{}, 错误: {}",
                             threadName, attempt, maxRetryAttempts, e.getMessage());
                    break;
                }
            }
        }

        // 抛出异常，让上层决定如何处理
        throw new RuntimeException("向量化服务不可用，已重试" + maxRetryAttempts + "次", lastException);
    }

    /**
     * 实际执行向量化请求的方法
     */
    private float[] doEmbed(String text) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("text", text);
            requestBody.put("model_name", "gte-Qwen2-1.5B-instruct");

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-APIKEY", apiKey);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求
            ResponseEntity<EmbeddingResponse> response = restTemplate.postForEntity(
                embeddingServiceUrl, request, EmbeddingResponse.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                EmbeddingResponse embeddingResponse = response.getBody();
                if (embeddingResponse.getCode() == 200 && embeddingResponse.getEmbedding() != null) {
                    // 转换 List<Double> 到 float[]
                    List<Double> embedding = embeddingResponse.getEmbedding();
                    float[] result = new float[embedding.size()];
                    for (int i = 0; i < embedding.size(); i++) {
                        result[i] = embedding.get(i).floatValue();
                    }
                    return result;
                } else {
                    throw new EmbeddingServiceException("向量化服务返回错误: " + embeddingResponse.getError_msg());
                }
            }

            throw new EmbeddingServiceException("向量化服务返回错误状态: " + response.getStatusCode());

        } catch (Exception e) {
            // 包装异常，便于重试判断
            if (e instanceof EmbeddingServiceException) {
                throw e;
            }
            throw new EmbeddingServiceException("向量化请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断异常是否应该重试（优化策略：减少重试场景）
     */
    private boolean shouldRetry(Exception e) {
        // 只对真正的网络连接问题进行重试
        if (e instanceof ConnectException ||
            e instanceof SocketTimeoutException) {
            return true;
        }

        // HTTP 429 (Too Many Requests) - 重试，但这是唯一的HTTP错误重试场景
        if (e instanceof HttpClientErrorException) {
            HttpClientErrorException httpError = (HttpClientErrorException) e;
            return httpError.getStatusCode().value() == 429;
        }

        // 不再重试以下异常，快速失败：
        // - ResourceAccessException (可能是配置问题)
        // - HttpServerErrorException (服务端问题，重试意义不大)
        // - EmbeddingServiceException (业务异常，重试无意义)

        return false;
    }

    /**
     * 计算重试延迟（优化策略：减少延迟时间，避免长时间阻塞线程）
     */
    private long calculateRetryDelay(int attempt) {
        // 线性增长而非指数增长，减少延迟时间
        long delay = baseRetryDelay * attempt;

        // 严格限制最大延迟，避免线程长时间阻塞
        delay = Math.min(delay, 3000);

        // 减少随机抖动范围，保持延迟可预测
        double jitter = 0.9 + (Math.random() * 0.2);
        delay = (long) (delay * jitter);

        return delay;
    }

    /**
     * 自定义异常类
     */
    public static class EmbeddingServiceException extends RuntimeException {
        public EmbeddingServiceException(String message) {
            super(message);
        }

        public EmbeddingServiceException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 响应实体类
     */
    @Data
    public static class EmbeddingResponse {
        private Integer code;
        private List<Double> embedding;
        private String error_msg;
    }
}
