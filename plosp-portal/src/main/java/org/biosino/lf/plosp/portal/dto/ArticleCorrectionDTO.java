package org.biosino.lf.plosp.portal.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 文献纠错DTO
 * <AUTHOR>
 */
@Data
public class ArticleCorrectionDTO {
    /**
     * 文献ID
     */
    @NotNull(message = "文献ID不能为空")
    private Long docId;

    /**
     * 纠错类型
     */
    @NotBlank(message = "纠错类型不能为空")
    private String correctionType;

    /**
     * 纠错内容
     */
    @NotBlank(message = "纠错内容不能为空")
    private String content;

    private String title;
}