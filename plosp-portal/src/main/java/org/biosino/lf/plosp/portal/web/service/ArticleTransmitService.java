package org.biosino.lf.plosp.portal.web.service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.biosino.lf.pds.article.custbean.dto.TransmitListQueryDTO;
import org.biosino.lf.pds.common.core.domain.R;
import org.biosino.lf.pds.common.core.page.PageDomain;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.plosp.portal.dto.ArticleTransmitSubmitDTO;
import org.biosino.lf.plosp.portal.dto.UploadListQueryDTO;
import org.biosino.lf.plosp.portal.dto.ArticleCorrectionDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文献传递服务层接口
 *
 * <AUTHOR>
 */
public interface ArticleTransmitService {
    R<String> articleTransmit(ArticleTransmitSubmitDTO transmitDTO);

    TableDataInfo transmitList(TransmitListQueryDTO dto, long userId, PageDomain pageDomain);

    void downloadArticlePDF(Long docId, Long userId, HttpServletRequest request, HttpServletResponse response);

    void downloadMyUploadPDF(Integer uploadId, Long userId, HttpServletRequest request, HttpServletResponse response);

    /**
     * 上传文献PDF文件
     */
    String upload(MultipartFile file, Long docId);

    TableDataInfo myUpload(UploadListQueryDTO queryDTO, Long userId, PageDomain pageDomain);

    /**
     * 提交文献纠错信息
     * @param correctionDTO 纠错信息DTO
     * @return 操作结果
     */
    R<String> submitCorrection(ArticleCorrectionDTO correctionDTO);

    TableDataInfo myCorrectionList(UploadListQueryDTO queryDTO, Long userId, PageDomain pageDomain);

}