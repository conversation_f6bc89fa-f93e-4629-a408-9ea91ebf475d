package org.biosino.lf.plosp.portal.es.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.mapper.ArticleMapper;
import org.biosino.lf.pds.article.mapper.ArticleMeshMapper;
import org.biosino.lf.pds.article.mapper.TbDdsMeshMapper;
import org.biosino.lf.pds.article.service.*;
import org.biosino.lf.plosp.portal.es.entity.PlospArticleEs;
import org.biosino.lf.plosp.portal.es.entity.ProcessResult;
import org.biosino.lf.plosp.portal.es.mapper.ArticleEsMapper;
import org.biosino.lf.plosp.portal.es.mapper.PlospArticleEsRepository;
import org.biosino.lf.plosp.portal.web.service.RemoteEmbeddingModel;
import org.springframework.ai.document.Document;
import org.springframework.ai.transformer.splitter.TextSplitter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EsService {

    private final ElasticsearchTemplate elasticsearchTemplate;
    private final RemoteEmbeddingModel remoteEmbeddingModel;
    private final TextSplitter textSplitter;
    private final PlospArticleEsRepository plospArticleEsRepository;
    private final ArticleMapper articleMapper;
    private final ArticleEsMapper articleEsMapper;
    private final IArticleDatabankService articleDatabankService;
    private final JournalService journalService;
    private final IArticleGrantService articleGrantService;
    private final IGrantService grantService;
    private final IArticlePubTypeService articlePubTypeService;
    private final IPubTypeService pubTypeService;
    private final IPublisherService publisherService;
    private final ITbDdsFileService tbDdsFileService;
    private final ITbDdsIfYearService tbDdsIfYearService;
    private final ITbDdsZkySectionService tbDdsZkySectionService;
    private final ThreadPoolMonitor threadPoolMonitor;
    private final TbDdsMeshMapper tbDdsMeshMapper;
    private final ArticleMeshMapper articleMeshMapper;
    private final RedisTemplate<Object, Object> redisTemplate;

    // 配置参数
    @Value("${es.batch.size:500}")
    private int batchSize;

    @Value("${es.thread.pool.core:4}")
    private int corePoolSize;

    @Value("${es.thread.pool.max:8}")
    private int maxPoolSize;

    @Value("${es.thread.pool.queue:1000}")
    private int queueCapacity;

    @Value("${es.vector.enabled:false}")
    private boolean vectorEnabled;

    // 线程池
    private ThreadPoolExecutor threadPoolExecutor;

    @PostConstruct
    public void init() {
        // 初始化线程池
        initThreadPool();

        // 初始化MeSH缓存
        CompletableFuture.runAsync(this::initMeshCache);
    }

    @PreDestroy
    public void destroy() {
        if (threadPoolExecutor != null && !threadPoolExecutor.isShutdown()) {
            log.info("开始关闭ES服务线程池");
            threadPoolMonitor.monitorThreadPool(threadPoolExecutor, "ES-Service");
            threadPoolMonitor.safeShutdown(threadPoolExecutor, "ES-Service", 30);
        }
    }

    /**
     * 初始化线程池
     */
    private void initThreadPool() {
        threadPoolExecutor = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueCapacity),
                new ThreadFactory() {
                    private final AtomicInteger threadNumber = new AtomicInteger(1);

                    @Override
                    public Thread newThread(Runnable r) {
                        Thread t = new Thread(r, "es-worker-" + threadNumber.getAndIncrement());
                        t.setDaemon(false);
                        // 设置未捕获异常处理器，防止线程意外死亡
                        t.setUncaughtExceptionHandler((thread, ex) -> {
                            log.error("线程 {} 发生未捕获异常，线程即将终止", thread.getName(), ex);
                            // 可以在这里添加监控告警
                        });
                        return t;
                    }
                },
                // 改为AbortPolicy，避免在主线程中执行任务
                new ThreadPoolExecutor.AbortPolicy()
        );
        log.info("线程池初始化完成: core={}, max={}, queue={}", corePoolSize, maxPoolSize, queueCapacity);
    }

    /**
     * 创建ES索引
     */
    private void createIndex() {
        IndexOperations indexOperations = elasticsearchTemplate.indexOps(PlospArticleEs.class);
        if (!indexOperations.exists()) {
            indexOperations.createWithMapping();
            log.info("创建索引成功: {}", PlospArticleEs.ES_INDEX_NAME);
        } else {
            log.info("索引已存在: {}", PlospArticleEs.ES_INDEX_NAME);
        }
    }

    /**
     * 强制重新创建ES索引（用于字段类型变更）
     */
    public void recreateIndex() {
        IndexOperations indexOperations = elasticsearchTemplate.indexOps(PlospArticleEs.class);

        if (indexOperations.exists()) {
            log.warn("删除现有索引: {}", PlospArticleEs.ES_INDEX_NAME);
            indexOperations.delete();
        }

        indexOperations.createWithMapping();
        log.info("重新创建索引成功: {}", PlospArticleEs.ES_INDEX_NAME);
    }

    /**
     * 批量转换Article为PlospArticleEs（使用MapStruct）
     */
    private List<PlospArticleEs> convertToEsEntities(List<Article> articles) {
        return articleEsMapper.articlesToEsArticles(articles);
    }

    /**
     * 第二阶段：为已存在的ES数据生成向量（批次间串行，批次内多线程）
     * 使用search_after进行深度分页查询ES数据
     */
    public void generateVectorsForExistingData() {
        if (!vectorEnabled) {
            log.info("向量生成已禁用，跳过向量生成");
            return;
        }

        final long start = System.currentTimeMillis();
        final AtomicInteger processedCount = new AtomicInteger(0);
        final AtomicInteger successCount = new AtomicInteger(0);
        final AtomicInteger failureCount = new AtomicInteger(0);

        log.info("开始为已存在的ES数据生成向量，批次大小: {}", batchSize);

        try {
            // 使用search_after进行深度分页查询ES数据
            Object[] searchAfter = null;
            int batchIndex = 0;

            while (true) {
                batchIndex++;
                final int currentBatchIndex = batchIndex;

                try {
                    long batchStart = System.currentTimeMillis();

                    // 构建ES查询，使用search_after进行深度分页
                    NativeQueryBuilder queryBuilder = new NativeQueryBuilder()
                            .withQuery(initFieldNotExistQuery("title_vector"))
                            .withSort(Sort.by(Sort.Direction.ASC, "id"))
                            .withMaxResults(batchSize)
                            .withTrackTotalHits(false); // 提高性能

                    // 设置search_after参数
                    if (searchAfter != null) {
                        queryBuilder.withSearchAfter(CollUtil.toList(searchAfter));
                    }

                    // 执行查询
                    SearchHits<PlospArticleEs> searchHits = elasticsearchTemplate.search(
                            queryBuilder.build(), PlospArticleEs.class);

                    List<PlospArticleEs> existingDocs = searchHits.getSearchHits().stream()
                            .map(SearchHit::getContent)
                            .collect(Collectors.toList());

                    if (existingDocs.isEmpty()) {
                        log.info("向量生成search_after分页查询完成，共处理{}个批次", batchIndex - 1);
                        break;
                    }

                    // 更新search_after游标
                    SearchHit<PlospArticleEs> lastHit = searchHits.getSearchHits().get(searchHits.getSearchHits().size() - 1);
                    searchAfter = lastHit.getSortValues().toArray();

                    // 批次内多线程生成向量（等待完成后再处理下一批次）
                    generateVectorsWithImprovedErrorHandling(existingDocs);

                    // 批量保存到ES（根据ID自动更新）
                    try {
                        plospArticleEsRepository.saveAll(existingDocs);
                        successCount.addAndGet(existingDocs.size());
                        log.debug("批次[{}]批量保存向量数据成功，共{}篇文章", currentBatchIndex, existingDocs.size());
                    } catch (Exception e) {
                        log.error("批次[{}]批量保存向量数据失败", currentBatchIndex, e);
                        failureCount.addAndGet(existingDocs.size());
                    }

                    long batchEnd = System.currentTimeMillis();
                    long batchTime = batchEnd - batchStart;

                    // 更新统计信息
                    int currentProcessed = processedCount.addAndGet(existingDocs.size());

                    log.info("向量批次[{}]完成，耗时: {}ms, 查询: {}, 成功: {}, 失败: {}, 已处理: {}条, 当前游标: {}",
                            currentBatchIndex, batchTime, existingDocs.size(),
                            successCount.get(), failureCount.get(), currentProcessed,
                            searchAfter != null && searchAfter.length > 0 ? searchAfter[0] : "null");

                    // 每处理一定批次后休息
                    if (currentBatchIndex % 50 == 0) {
                        log.info("已处理{}个批次，休息200ms", currentBatchIndex);
                        Thread.sleep(200);
                    }

                } catch (Exception e) {
                    log.error("向量批次[{}]处理失败", currentBatchIndex, e);
                    failureCount.addAndGet(batchSize);

                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            final long end = System.currentTimeMillis();
            log.warn("向量生成完成统计 - 已处理: {}, 成功: {}, 失败: {}, 总耗时: {}ms",
                    processedCount.get(), successCount.get(), failureCount.get(), (end - start));

        } catch (Exception e) {
            log.error("向量生成失败", e);
            throw new RuntimeException("向量生成失败", e);
        }
    }

    private Query initFieldNotExistQuery(final String field) {
        return QueryBuilders.bool(x -> x.mustNot(QueryBuilders.exists(e -> e.field(field))));
    }

    /**
     * 第三阶段：为已存在的ES数据处理MeSH关系（批次间串行，批次内多线程）
     */
    /**
     * 为已存在的ES数据处理MeSH关系（批次间串行，批次内多线程）
     * 使用search_after进行深度分页查询ES数据
     */
    public void processMeshForExistingData() {
        final long start = System.currentTimeMillis();
        final AtomicInteger processedCount = new AtomicInteger(0);
        final AtomicInteger successCount = new AtomicInteger(0);
        final AtomicInteger failureCount = new AtomicInteger(0);

        log.info("开始为已存在的ES数据处理MeSH关系，批次大小: {}", batchSize);

        try {
            // 使用search_after进行深度分页查询ES数据
            Object[] searchAfter = null;
            int batchIndex = 0;

            while (true) {
                batchIndex++;
                final int currentBatchIndex = batchIndex;

                try {
                    long batchStart = System.currentTimeMillis();

                    // 构建ES查询，使用search_after进行深度分页
                    NativeQueryBuilder queryBuilder = new NativeQueryBuilder()
                            .withQuery(initFieldNotExistQuery("mesh_ui"))
                            .withSort(Sort.by(Sort.Direction.ASC, "id"))
                            .withMaxResults(batchSize)
                            .withTrackTotalHits(false); // 提高性能

                    // 设置search_after参数
                    if (searchAfter != null) {
                        queryBuilder.withSearchAfter(CollUtil.toList(searchAfter));
                    }

                    // 执行查询
                    SearchHits<PlospArticleEs> searchHits = elasticsearchTemplate.search(
                            queryBuilder.build(), PlospArticleEs.class);

                    List<PlospArticleEs> existingDocs = searchHits.getSearchHits().stream()
                            .map(SearchHit::getContent)
                            .collect(Collectors.toList());

                    if (existingDocs.isEmpty()) {
                        log.info("MeSH处理search_after分页查询完成，共处理{}个批次", batchIndex - 1);
                        break;
                    }

                    // 更新search_after游标
                    SearchHit<PlospArticleEs> lastHit = searchHits.getSearchHits().get(searchHits.getSearchHits().size() - 1);
                    searchAfter = lastHit.getSortValues().toArray();

                    // 批次内多线程处理MeSH数据（等待完成后再处理下一批次）
                    processMeshDataAsync(existingDocs, currentBatchIndex);

                    // 批量保存到ES（根据ID自动更新）
                    try {
                        plospArticleEsRepository.saveAll(existingDocs);
                        successCount.addAndGet(existingDocs.size());
                        log.debug("批次[{}]批量保存MeSH数据成功，共{}篇文章", currentBatchIndex, existingDocs.size());
                    } catch (Exception e) {
                        log.error("批次[{}]批量保存MeSH数据失败", currentBatchIndex, e);
                        failureCount.addAndGet(existingDocs.size());
                    }

                    long batchEnd = System.currentTimeMillis();
                    long batchTime = batchEnd - batchStart;

                    // 更新统计信息
                    int currentProcessed = processedCount.addAndGet(existingDocs.size());

                    log.info("MeSH批次[{}]完成，耗时: {}ms, 查询: {}, 成功: {}, 失败: {}, 已处理: {}条, 当前游标: {}",
                            currentBatchIndex, batchTime, existingDocs.size(),
                            successCount.get(), failureCount.get(), currentProcessed,
                            searchAfter != null && searchAfter.length > 0 ? searchAfter[0] : "null");

                    // 每处理一定批次后休息
                    if (currentBatchIndex % 50 == 0) {
                        Thread.sleep(200);
                    }

                } catch (Exception e) {
                    log.error("MeSH批次[{}]处理失败", currentBatchIndex, e);
                    failureCount.addAndGet(batchSize);

                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            final long end = System.currentTimeMillis();
            log.warn("MeSH处理完成统计 - 已处理: {}, 成功: {}, 失败: {}, 总耗时: {}ms",
                    processedCount.get(), successCount.get(), failureCount.get(), (end - start));

        } catch (Exception e) {
            log.error("MeSH处理失败", e);
            throw new RuntimeException("MeSH处理失败", e);
        }
    }

    /**
     * 全库清空全量更新
     */
    public void fullRefresh() {
        final long start = System.currentTimeMillis();
        log.warn("开始全量更新索引: {}", PlospArticleEs.ES_INDEX_NAME);

        try {
            // 分批查询并处理数据
            processAllArticlesInBatches();

            final long end = System.currentTimeMillis();
            log.warn("全量更新完成: {}, 耗时: {}ms", PlospArticleEs.ES_INDEX_NAME, (end - start));

        } catch (Exception e) {
            log.error("全量更新失败", e);
            throw new RuntimeException("全量更新失败", e);
        }
    }

    /**
     * 清空现有ES数据 - 针对大数据量优化
     */
    private void clearExistingData() {
        try {
            log.info("开始清空ES索引数据...");
            long startTime = System.currentTimeMillis();

            // 检查索引是否存在以及数据量
            boolean indexExists = elasticsearchTemplate.indexOps(PlospArticleEs.class).exists();
            if (!indexExists) {
                log.info("索引不存在，无需清空");
                return;
            }

            // 获取当前索引中的数据量
            long currentCount = plospArticleEsRepository.count();
            log.info("当前ES索引中有 {} 条数据", currentCount);

            if (currentCount == 0) {
                log.info("索引为空，无需清空");
                return;
            }

            // 对于大数据量，使用删除索引重建的方式更高效
            if (currentCount > 1_000_000) {
                log.warn("检测到大数据量({})，使用删除索引重建方式", currentCount);

                // 删除整个索引
                elasticsearchTemplate.indexOps(PlospArticleEs.class).delete();
                log.info("删除索引完成");

                // 重新创建索引
                createIndex();
                log.info("重新创建索引完成");
            } else {
                // 小数据量使用deleteAll
                log.info("使用deleteAll方式清空数据");
                plospArticleEsRepository.deleteAll();
                log.info("deleteAll完成");
            }

            long endTime = System.currentTimeMillis();
            log.info("清空索引数据完成，耗时: {}ms", (endTime - startTime));

        } catch (Exception e) {
            log.error("清空索引数据失败", e);
            throw new RuntimeException("清空索引数据失败", e);
        }
    }

    /**
     * 分批查询并处理所有文章数据 - 使用游标分页优化大数据量查询
     */
    private void processAllArticlesInBatches() {
        final long start = System.currentTimeMillis();

        final AtomicInteger processedCount = new AtomicInteger(0);
        final AtomicInteger successCount = new AtomicInteger(0);
        final AtomicInteger failureCount = new AtomicInteger(0);

        log.info("{}开始，批次大小: {}, 使用游标分页优化SQL查询",
                "全量更新", batchSize);

        // 使用游标分页，避免大offset问题
        Long lastId = 0L;
        int batchIndex = 0;

        while (true) {
            batchIndex++;
            final int currentBatchIndex = batchIndex;

            try {
                long batchStart = System.currentTimeMillis();

                int currentBatchSize = batchSize;

                // 使用QueryWrapper查询 - 游标分页，避免复杂JOIN
                QueryWrapper<Article> queryWrapper = new QueryWrapper<>();
                if (lastId != null) {
                    queryWrapper.gt("id", lastId);
                }
                queryWrapper.orderByAsc("id");
                queryWrapper.last("LIMIT " + currentBatchSize);

                List<Article> articles = articleMapper.selectList(queryWrapper);

                if (articles.isEmpty()) {
                    log.info("游标分页查询完成，共处理{}个批次", batchIndex - 1);
                    break;
                }

                // 更新游标
                lastId = articles.get(articles.size() - 1).getId();

                // 处理单个批次
                ProcessResult result = processBatch(articles, "全量更新", currentBatchIndex);

                long batchEnd = System.currentTimeMillis();
                long batchTime = batchEnd - batchStart;

                // 更新统计信息
                int currentProcessed = processedCount.addAndGet(result.getProcessedCount());
                successCount.addAndGet(result.getSuccessCount());
                failureCount.addAndGet(result.getFailureCount());

                // 不计算总进度（因为不知道总数），只显示当前处理数量
                log.info("批次[{}]完成，耗时: {}ms, 查询: {}, 成功: {}, 失败: {}, 已处理: {}条, 当前游标ID: {}",
                        currentBatchIndex, batchTime, articles.size(),
                        result.getSuccessCount(), result.getFailureCount(), currentProcessed, lastId);

                // 针对SQL优化：每处理一定批次后休息，避免数据库压力过大
                if (currentBatchIndex % 50 == 0) {
                    log.info("已处理{}个批次，休息200ms以减轻SQL压力", currentBatchIndex);
                    Thread.sleep(200);
                }

            } catch (Exception e) {
                log.error("批次[{}]处理失败", currentBatchIndex, e);
                failureCount.addAndGet(batchSize);

                // 如果是数据库连接问题，稍作等待后继续
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        final long end = System.currentTimeMillis();
        log.warn("{}完成统计 - 处理: {}, 成功: {}, 失败: {}, 总耗时: {}ms, 平均速度: {:.2f}条/秒",
                "全量更新", processedCount.get(), successCount.get(), failureCount.get(), (end - start),
                (double) successCount.get() / ((end - start) / 1000.0));
    }

    /**
     * 增量更新（基于update_time）
     */
    public void incrementalUpdate(LocalDateTime lastUpdateTime) {
        final long start = System.currentTimeMillis();
        log.warn("开始增量更新索引: {}, 更新时间: {}", PlospArticleEs.ES_INDEX_NAME, lastUpdateTime);

        try {
            // 使用QueryWrapper查询 - 时间范围查询
            QueryWrapper<Article> queryWrapper = new QueryWrapper<>();
            if (lastUpdateTime != null) {
                queryWrapper.ge("update_time", lastUpdateTime);
            }
            queryWrapper.orderByAsc("id");
            List<Article> articleList = articleMapper.selectList(queryWrapper);

            log.info("查询到 {} 条需要更新的文章数据", articleList.size());

            if (articleList.isEmpty()) {
                log.info("没有需要更新的数据，增量更新结束");
                return;
            }

            // 多线程处理
            processArticlesInParallel(articleList, "增量更新");

            final long end = System.currentTimeMillis();
            log.warn("增量更新完成: {}, 耗时: {}ms, 共处理: {}条数据",
                    PlospArticleEs.ES_INDEX_NAME, (end - start), articleList.size());

        } catch (Exception e) {
            log.error("增量更新失败", e);
            throw new RuntimeException("增量更新失败", e);
        }
    }

    /**
     * 指定ID更新
     */
    public void updateByIds(List<Long> articleIds) {
        final long start = System.currentTimeMillis();
        log.warn("开始按ID更新索引: {}, ID数量: {}", PlospArticleEs.ES_INDEX_NAME, articleIds.size());

        try {
            if (articleIds.isEmpty()) {
                log.warn("ID列表为空，更新结束");
                return;
            }

            // 使用QueryWrapper查询 - ID列表查询
            QueryWrapper<Article> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", articleIds);
            queryWrapper.orderByAsc("id");
            List<Article> articleList = articleMapper.selectList(queryWrapper);

            log.info("根据ID查询到 {} 条文章数据", articleList.size());

            if (articleList.isEmpty()) {
                log.warn("根据ID没有查询到数据，更新结束");
                return;
            }

            // 多线程处理
            processArticlesInParallel(articleList, "ID更新");

            final long end = System.currentTimeMillis();
            log.warn("ID更新完成: {}, 耗时: {}ms, 共处理: {}条数据",
                    PlospArticleEs.ES_INDEX_NAME, (end - start), articleList.size());

        } catch (Exception e) {
            log.error("ID更新失败", e);
            throw new RuntimeException("ID更新失败", e);
        }
    }

    /**
     * 多线程并行处理文章数据
     */
    private void processArticlesInParallel(List<Article> articles, String operationType) {
        if (articles.isEmpty()) {
            return;
        }

        final int totalCount = articles.size();
        final AtomicInteger processedCount = new AtomicInteger(0);
        final AtomicInteger successCount = new AtomicInteger(0);
        final AtomicInteger failureCount = new AtomicInteger(0);
        final AtomicLong totalProcessTime = new AtomicLong(0);

        log.info("开始{}多线程处理，总数据量: {}, 批次大小: {}", operationType, totalCount, batchSize);

        // 分批处理
        List<List<Article>> batches = new ArrayList<>();
        for (int i = 0; i < totalCount; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalCount);
            batches.add(articles.subList(i, endIndex));
        }

        log.info("分为 {} 个批次进行处理", batches.size());

        // 使用CountDownLatch等待所有任务完成
        CountDownLatch latch = new CountDownLatch(batches.size());

        // 提交所有批次任务
        for (int i = 0; i < batches.size(); i++) {
            final List<Article> batch = batches.get(i);
            final int batchIndex = i + 1;

            threadPoolExecutor.submit(() -> {
                try {
                    long batchStart = System.currentTimeMillis();

                    // 处理单个批次
                    ProcessResult result = processBatch(batch, operationType, batchIndex);

                    long batchEnd = System.currentTimeMillis();
                    long batchTime = batchEnd - batchStart;
                    totalProcessTime.addAndGet(batchTime);

                    // 更新统计信息
                    int currentProcessed = processedCount.addAndGet(result.getProcessedCount());
                    successCount.addAndGet(result.getSuccessCount());
                    failureCount.addAndGet(result.getFailureCount());

                    // 计算进度
                    double progress = (double) currentProcessed / totalCount * 100;

                    log.info("批次[{}/{}]完成，耗时: {}ms, 成功: {}, 失败: {}, 总进度: {:.2f}%",
                            batchIndex, batches.size(), batchTime,
                            result.getSuccessCount(), result.getFailureCount(), progress);

                } catch (Exception e) {
                    log.error("批次[{}]处理失败", batchIndex, e);
                    failureCount.addAndGet(batch.size());
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有任务完成
        try {
            boolean finished = latch.await(30, TimeUnit.MINUTES);
            if (!finished) {
                log.error("{}处理超时，强制结束", operationType);
                throw new RuntimeException(operationType + "处理超时");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(operationType + "处理被中断", e);
        }

        // 输出最终统计
        log.warn("{}完成统计 - 总数: {}, 成功: {}, 失败: {}, 总耗时: {}ms, 平均耗时: {}ms/批次",
                operationType, totalCount, successCount.get(), failureCount.get(),
                totalProcessTime.get(), totalProcessTime.get() / batches.size());
    }

    /**
     * 处理单个批次
     * 处理单个批次
     */
    private ProcessResult processBatch(List<Article> batch, String operationType, int batchIndex) {
        ProcessResult result = new ProcessResult();
        List<PlospArticleEs> esArticles;

        try {
            // 1. 获取文章ID列表
            List<Long> articleIds = batch.stream().map(Article::getId).collect(Collectors.toList());

            // 2. 使用QueryWrapper查询基金号信息
            enrichArticlesWithGrantInfo(batch, articleIds);

            // 3. 使用MapStruct批量转换为ES实体
            esArticles = convertToEsEntities(batch);
            result.setSuccessCount(esArticles.size());

            // 如果转换数量不匹配，说明有转换失败的
            if (esArticles.size() != batch.size()) {
                int failedCount = batch.size() - esArticles.size();
                result.setFailureCount(failedCount);
                log.warn("批次[{}]有{}条数据转换失败", batchIndex, failedCount);
            }

            // 基础数据处理：HTML清理和文本规范化
            if (!esArticles.isEmpty()) {
                for (PlospArticleEs esArticle : esArticles) {
                    if (esArticle.getArticleAbstract() != null) {
                        esArticle.setArticleAbstract(cleanAndNormalizeText(esArticle.getArticleAbstract()));
                    }
                    // 清理标题的HTML标签
                    if (esArticle.getTitle() != null) {
                        esArticle.setTitle(cleanAndNormalizeText(esArticle.getTitle()));
                    }
                }

                try {
                    plospArticleEsRepository.saveAll(esArticles);
                } catch (Exception e) {
                    // 如果批量保存失败，尝试单个保存
                    int savedCount = saveIndividually(esArticles, batchIndex);
                    result.setSuccessCount(savedCount);
                    result.setFailureCount(esArticles.size() - savedCount);
                }
            }

        } catch (Exception e) {
            log.error("批次[{}]处理异常", batchIndex, e);
            result.setFailureCount(batch.size());
        }

        result.setProcessedCount(batch.size());
        return result;
    }

    /**
     * 单个保存（当批量保存失败时的备用方案）
     */
    private int saveIndividually(List<PlospArticleEs> esArticles, int batchIndex) {
        int savedCount = 0;
        for (PlospArticleEs esArticle : esArticles) {
            try {
                plospArticleEsRepository.save(esArticle);
                savedCount++;
            } catch (Exception e) {
                log.error("批次[{}]单个保存失败，ID: {}", batchIndex, esArticle.getId(), e);
            }
        }
        log.info("批次[{}]单个保存完成，成功: {}/{}", batchIndex, savedCount, esArticles.size());
        return savedCount;
    }

    /**
     * 使用QueryWrapper查询基金号和publisher_id信息并填充到Article对象中
     */
    private void enrichArticlesWithGrantInfo(List<Article> articles, List<Long> articleIds) {
        try {
            if (CollUtil.isEmpty(articleIds)) {
                return;
            }

            // 1. 查询期刊信息获取publisher_id
            enrichArticlesWithPublisherInfo(articles);

            // 2. 查询databank信息
            enrichArticlesWithDatabankInfo(articles, articleIds);

            // 3. 查询pubtype信息
            enrichArticlesWithPubtypeInfo(articles, articleIds);

            // 4. 设置free字段值
            enrichArticlesWithFreeInfo(articles);

            // 5. 设置hasPdf字段值
            enrichArticlesWithHasPdfInfo(articles, articleIds);

            // 6. 查询影响因子、JCR分区和中科院分区信息
            enrichArticlesWithJournalMetricsInfo(articles);

            // 7. 查询文章-基金关联表
            QueryWrapper<ArticleGrant> articleGrantWrapper = new QueryWrapper<>();
            articleGrantWrapper.in("doc_id", articleIds);
            articleGrantWrapper.select("doc_id", "grant_id");
            List<ArticleGrant> articleGrants = articleGrantService.list(articleGrantWrapper);

            if (CollUtil.isEmpty(articleGrants)) {
                log.debug("没有找到基金号信息，文章ID: {}", articleIds);
                return;
            }

            // 4. 获取基金ID列表
            List<Long> grantIds = articleGrants.stream()
                    .map(ArticleGrant::getGrantId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(grantIds)) {
                return;
            }

            // 5. 查询基金详细信息
            QueryWrapper<Grant> grantWrapper = new QueryWrapper<>();
            grantWrapper.in("id", grantIds);
            grantWrapper.select("id", "grant_id");
            List<Grant> grants = grantService.list(grantWrapper);

            // 6. 构建基金ID到基金号的映射
            Map<Long, String> grantIdToGrantNumberMap = grants.stream()
                    .filter(grant -> StrUtil.isNotBlank(grant.getGrantId()))
                    .collect(Collectors.toMap(Grant::getId, Grant::getGrantId, (v1, v2) -> v1));

            // 7. 构建文章ID到基金号列表的映射
            Map<Long, List<String>> articleIdToGrantsMap = articleGrants.stream()
                    .filter(ag -> ag.getGrantId() != null && grantIdToGrantNumberMap.containsKey(ag.getGrantId()))
                    .collect(Collectors.groupingBy(
                            ArticleGrant::getDocId,
                            Collectors.mapping(
                                    ag -> grantIdToGrantNumberMap.get(ag.getGrantId()),
                                    Collectors.toList()
                            )
                    ));

            // 8. 将基金号信息设置到Article对象中（List<String>类型）
            for (Article article : articles) {
                List<String> grantNumbers = articleIdToGrantsMap.get(article.getId());
                if (CollUtil.isNotEmpty(grantNumbers)) {
                    article.setGrant(grantNumbers);
                }
            }

        } catch (Exception e) {
            log.error("查询基金号信息失败", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 使用QueryWrapper查询期刊信息获取publisher_id、期刊名称和出版社名称
     */
    private void enrichArticlesWithPublisherInfo(List<Article> articles) {
        try {
            if (CollUtil.isEmpty(articles)) {
                return;
            }

            // 1. 获取期刊ID列表
            List<Long> journalIds = articles.stream()
                    .map(Article::getJournalId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(journalIds)) {
                log.debug("没有期刊ID，跳过publisher_id查询");
                return;
            }

            // 2. 使用QueryWrapper查询期刊信息
            QueryWrapper<Journal> journalWrapper = new QueryWrapper<>();
            journalWrapper.in("id", journalIds);
            journalWrapper.select("id", "publisher_id", "title");
            List<Journal> journals = journalService.list(journalWrapper);

            if (CollUtil.isEmpty(journals)) {
                log.debug("没有找到期刊信息，期刊ID: {}", journalIds);
                return;
            }

            // 3. 获取出版社ID列表并查询出版社信息
            List<Long> publisherIds = journals.stream()
                    .map(Journal::getPublisherId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            Map<Long, String> publisherIdToNameMap = new HashMap<>();
            if (CollUtil.isNotEmpty(publisherIds)) {
                QueryWrapper<Publisher> publisherWrapper = new QueryWrapper<>();
                publisherWrapper.in("id", publisherIds);
                publisherWrapper.select("id", "name");
                List<Publisher> publishers = publisherService.list(publisherWrapper);

                publisherIdToNameMap = publishers.stream()
                        .filter(publisher -> StrUtil.isNotBlank(publisher.getName()))
                        .collect(Collectors.toMap(Publisher::getId, Publisher::getName, (v1, v2) -> v1));
            }

            // 4. 构建期刊ID到各种信息的映射
            Map<Long, Long> journalIdToPublisherIdMap = journals.stream()
                    .filter(journal -> journal.getPublisherId() != null)
                    .collect(Collectors.toMap(Journal::getId, Journal::getPublisherId, (v1, v2) -> v1));

            Map<Long, String> journalIdToNameMap = journals.stream()
                    .filter(journal -> StrUtil.isNotBlank(journal.getTitle()))
                    .collect(Collectors.toMap(Journal::getId, Journal::getTitle, (v1, v2) -> v1));

            // 5. 将期刊和出版社信息设置到Article对象中
            for (Article article : articles) {
                if (article.getJournalId() != null) {
                    Long journalId = article.getJournalId();

                    // 设置publisher_id
                    Long publisherId = journalIdToPublisherIdMap.get(journalId);
                    if (publisherId != null) {
                        article.setPublisherId(publisherId);

                        // 设置出版社名称
                        String publisherName = publisherIdToNameMap.get(publisherId);
                        if (StrUtil.isNotBlank(publisherName)) {
                            article.setPublisherName(publisherName);
                        }
                    }

                    // 设置期刊名称
                    String journalName = journalIdToNameMap.get(journalId);
                    if (StrUtil.isNotBlank(journalName)) {
                        article.setJournalName(journalName);
                    }
                }
            }

        } catch (Exception e) {
            log.error("查询publisher_id信息失败", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 使用QueryWrapper查询databank信息
     */
    private void enrichArticlesWithDatabankInfo(List<Article> articles, List<Long> articleIds) {
        try {
            if (CollUtil.isEmpty(articleIds)) {
                return;
            }

            // 1. 使用QueryWrapper查询tb_dds_article_databank表
            QueryWrapper<ArticleDatabank> databankWrapper = new QueryWrapper<>();
            databankWrapper.in("doc_id", articleIds);
            databankWrapper.select("doc_id", "value");
            List<ArticleDatabank> articleDatabanks = articleDatabankService.list(databankWrapper);

            if (CollUtil.isEmpty(articleDatabanks)) {
                log.debug("没有找到databank信息，文章ID: {}", articleIds);
                return;
            }

            // 2. 构建文章ID到databank列表的映射
            Map<Long, List<String>> articleIdToDatabankMap = articleDatabanks.stream()
                    .filter(ad -> StrUtil.isNotBlank(ad.getValue()))
                    .collect(Collectors.groupingBy(
                            ArticleDatabank::getDocId,
                            Collectors.mapping(
                                    ArticleDatabank::getValue,
                                    Collectors.toList()
                            )
                    ));

            // 3. 将databank信息设置到Article对象中（List<String>类型）
            for (Article article : articles) {
                List<String> databankList = articleIdToDatabankMap.get(article.getId());
                if (CollUtil.isNotEmpty(databankList)) {
                    article.setDatabank(databankList);
                }
            }


        } catch (Exception e) {
            log.error("查询databank信息失败", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 使用QueryWrapper查询pubtype信息并设置到Article.pubType字段中
     */
    private void enrichArticlesWithPubtypeInfo(List<Article> articles, List<Long> articleIds) {
        try {
            if (CollUtil.isEmpty(articleIds)) {
                return;
            }

            // 1. 使用QueryWrapper查询tb_dds_article_pubtype表
            QueryWrapper<ArticlePubType> articlePubtypeWrapper = new QueryWrapper<>();
            articlePubtypeWrapper.in("doc_id", articleIds);
            articlePubtypeWrapper.select("doc_id", "pubtype_id");
            List<ArticlePubType> articlePubtypes = articlePubTypeService.list(articlePubtypeWrapper);

            if (CollUtil.isEmpty(articlePubtypes)) {
                log.debug("没有找到pubtype关联信息，文章ID: {}", articleIds);
                return;
            }

            // 2. 获取pubtype_id列表
            List<Long> pubtypeIds = articlePubtypes.stream()
                    .map(ArticlePubType::getPubtypeId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(pubtypeIds)) {
                return;
            }

            // 3. 使用QueryWrapper查询tb_dds_pubtype表
            QueryWrapper<PubType> pubtypeWrapper = new QueryWrapper<>();
            pubtypeWrapper.in("id", pubtypeIds);
            pubtypeWrapper.select("id", "pub_type");
            List<PubType> pubtypes = pubTypeService.list(pubtypeWrapper);

            if (CollUtil.isEmpty(pubtypes)) {
                log.debug("没有找到pubtype信息，pubtypeIds: {}", pubtypeIds);
                return;
            }

            // 4. 构建pubtype_id到pubtype的映射
            Map<Long, String> pubtypeIdToPubtypeMap = pubtypes.stream()
                    .filter(pt -> StrUtil.isNotBlank(pt.getPubType()))
                    .collect(Collectors.toMap(PubType::getId, PubType::getPubType, (v1, v2) -> v1));

            // 5. 构建文章ID到pubtype列表的映射
            Map<Long, List<String>> articleIdToPubtypeMap = articlePubtypes.stream()
                    .filter(apt -> apt.getPubtypeId() != null && pubtypeIdToPubtypeMap.containsKey(apt.getPubtypeId()))
                    .collect(Collectors.groupingBy(
                            ArticlePubType::getDocId,
                            Collectors.mapping(
                                    apt -> pubtypeIdToPubtypeMap.get(apt.getPubtypeId()),
                                    Collectors.toList()
                            )
                    ));

            // 6. 将pubtype信息设置到Article对象的pubType字段中（List<String>类型）
            for (Article article : articles) {
                List<String> pubtypeList = articleIdToPubtypeMap.get(article.getId());
                if (CollUtil.isNotEmpty(pubtypeList)) {
                    article.setPubType(pubtypeList);
                }
            }

        } catch (Exception e) {
            log.error("查询pubtype信息失败", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 设置文章的free字段值
     * 规则：Source包含任意PMC, BioRxiv, MedRxiv的都是免费(true)，否则都是付费(false)
     */
    private void enrichArticlesWithFreeInfo(List<Article> articles) {
        try {
            if (CollUtil.isEmpty(articles)) {
                return;
            }

            // 定义免费来源关键词
            List<String> freeSources = List.of("PMC", "BioRxiv", "MedRxiv");

            for (Article article : articles) {
                boolean isFree = false;

                // 检查source字段
                List<String> sources = article.getSource();
                if (CollUtil.isNotEmpty(sources)) {
                    // 检查是否包含任意一个免费来源
                    isFree = sources.stream()
                            .anyMatch(source -> freeSources.stream()
                                    .anyMatch(freeSource -> StrUtil.containsIgnoreCase(source, freeSource)));
                }

                // 设置free字段
                article.setFree(isFree);
            }

        } catch (Exception e) {
            log.error("设置free字段失败", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 设置文章的hasPdf字段值
     * 规则：通过tb_dds_file查询tb_dds_article的id字段且type字段是PDF，如果存在记录则为true，否则为false
     */
    private void enrichArticlesWithHasPdfInfo(List<Article> articles, List<Long> articleIds) {
        try {
            if (CollUtil.isEmpty(articleIds)) {
                return;
            }

            // 1. 使用QueryWrapper查询tb_dds_file表，查找type为PDF的文件
            QueryWrapper<TbDdsFile> fileWrapper = new QueryWrapper<>();
            fileWrapper.in("doc_id", articleIds);
            fileWrapper.eq("type", "PDF");
            fileWrapper.select("doc_id");
            List<TbDdsFile> pdfFiles = tbDdsFileService.list(fileWrapper);

            // 2. 构建有PDF文件的文章ID集合
            Set<Long> articleIdsWithPdf = pdfFiles.stream()
                    .map(TbDdsFile::getDocId)
                    .collect(Collectors.toSet());

            // 3. 为每篇文章设置hasPdf字段
            for (Article article : articles) {
                boolean hasPdf = articleIdsWithPdf.contains(article.getId());
                article.setHasPdf(hasPdf);
            }

        } catch (Exception e) {
            log.error("设置hasPdf字段失败", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 改进的向量生成方法，重点解决线程宕机连锁反应问题
     */
    private void generateVectorsWithImprovedErrorHandling(List<PlospArticleEs> esArticles) {
        if (esArticles.isEmpty()) {
            return;
        }

        log.info("开始为 {} 篇文章生成向量", esArticles.size());

        // 使用信号量控制并发数，避免线程池过载
        Semaphore semaphore = new Semaphore(Math.min(maxPoolSize, esArticles.size()));
        CountDownLatch latch = new CountDownLatch(esArticles.size());
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);

        for (PlospArticleEs esArticle : esArticles) {
            try {
                // 获取信号量许可
                semaphore.acquire();

                // 提交任务到线程池
                threadPoolExecutor.submit(() -> {
                    try {
                        generateVectorsSafely(esArticle);
                        successCount.incrementAndGet();
                    } catch (Exception e) {
                        log.error("生成向量失败，文章ID: {}, 线程: {}",
                                esArticle.getId(), Thread.currentThread().getName(), e);
                        errorCount.incrementAndGet();
                        // 关键：异常被完全捕获，不会影响其他线程
                    } finally {
                        semaphore.release(); // 释放信号量
                        latch.countDown();   // 计数器减1
                    }
                });

            } catch (InterruptedException e) {
                log.warn("获取信号量被中断，跳过文章 {}", esArticle.getId());
                latch.countDown();
                Thread.currentThread().interrupt();
            } catch (RejectedExecutionException e) {
                log.warn("线程池拒绝执行，跳过文章 {}", esArticle.getId());
                semaphore.release();
                latch.countDown();
                errorCount.incrementAndGet();
            }
        }

        // 等待所有任务完成，设置合理的超时时间
        try {
            boolean finished = latch.await(10, TimeUnit.MINUTES);
            if (!finished) {
                log.warn("向量生成超时，可能有任务未完成");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("向量生成被中断", e);
        }

        log.info("向量生成完成，成功: {}, 失败: {}", successCount.get(), errorCount.get());
    }

    /**
     * 线程安全的向量生成方法 - 专注于异常隔离
     */
    private void generateVectorsSafely(PlospArticleEs esArticle) {
        Thread currentThread = Thread.currentThread();
        String originalName = currentThread.getName();

        try {
            // 设置线程名称便于调试
            currentThread.setName(originalName + "-article-" + esArticle.getId());

            // 执行向量生成
            generateVectors(esArticle);

            log.debug("文章 {} 向量生成成功", esArticle.getId());

        } catch (OutOfMemoryError e) {
            // OOM错误特殊处理，避免影响其他线程
            log.error("向量生成OOM，文章ID: {}, 线程: {}", esArticle.getId(), currentThread.getName());
            // 清理可能的大对象
            esArticle.setTitleVector(null);
            esArticle.setAbstractVectors(null);
            // 建议GC
            System.gc();
            throw new RuntimeException("OOM during vector generation", e);

        } catch (StackOverflowError e) {
            // 栈溢出错误特殊处理
            log.error("向量生成栈溢出，文章ID: {}, 线程: {}", esArticle.getId(), currentThread.getName());
            throw new RuntimeException("StackOverflow during vector generation", e);

        } catch (Exception e) {
            // 普通异常处理，确保不会传播到其他线程
            log.error("向量生成失败，文章ID: {}, 线程: {}, 错误: {}",
                    esArticle.getId(), currentThread.getName(), e.getMessage());

            // 设置默认值，避免ES映射错误
            esArticle.setTitleVector(null);
            esArticle.setAbstractVectors(null);

            // 不重新抛出异常，避免影响其他线程

        } catch (Throwable t) {
            // 捕获所有可能的错误，包括Error类型
            log.error("向量生成发生严重错误，文章ID: {}, 线程: {}",
                    esArticle.getId(), currentThread.getName(), t);

            // 设置默认值
            esArticle.setTitleVector(null);
            esArticle.setAbstractVectors(null);

            // 对于严重错误，也不抛出，保护其他线程

        } finally {
            // 恢复线程名称
            currentThread.setName(originalName);
        }
    }

    /**
     * 为ES实体生成向量
     */
    private void generateVectors(PlospArticleEs esArticle) {
        // 检查是否启用向量生成
        if (!vectorEnabled) {
            log.debug("向量生成已禁用，跳过文章 {}", esArticle.getId());
            esArticle.setTitleVector(null);
            esArticle.setAbstractVectors(null);
            return;
        }

        try {
            // 1. 生成标题向量（不分块）
            if (StrUtil.isNotBlank(esArticle.getTitle())) {
                esArticle.setTitleVector(normalizeVector(remoteEmbeddingModel, esArticle.getTitle()));
                log.debug("为文章 {} 生成标题向量", esArticle.getId());
            }

            // 2. 生成摘要向量（分块处理，存储为nested结构）
            if (StrUtil.isNotBlank(esArticle.getArticleAbstract())) {
                List<PlospArticleEs.AbstractVector> abstractVectors = generateAbstractVectors(esArticle.getArticleAbstract());
                esArticle.setAbstractVectors(abstractVectors);
                log.debug("为文章 {} 生成摘要向量，分块数: {}", esArticle.getId(),
                        abstractVectors != null ? abstractVectors.size() : 0);
            }

        } catch (Exception e) {
            log.error("生成向量失败，文章ID: {}", esArticle.getId(), e);
            // 向量生成失败时，设置为null，避免ES映射错误
            esArticle.setTitleVector(null);
            esArticle.setAbstractVectors(null);
        }
    }

    /**
     * 向量生成和归一化方法
     */
    public static float[] normalizeVector(RemoteEmbeddingModel remoteModel, String text) {
        final float[] vector = remoteModel.embed(text);

        // 检查是否需要归一化（计算向量的模长）
        double squareSum = 0.0;
        for (float v : vector) {
            squareSum += v * v;
        }
        final double norm = Math.sqrt(squareSum);

        // 如果模长接近1，说明已经归一化了
        if (Math.abs(norm - 1.0) < 0.01) {
            // 已经归一化，直接返回
            return vector;
        } else {
            // 需要归一化
            float[] normalized = new float[vector.length];
            for (int i = 0; i < vector.length; i++) {
                normalized[i] = (float) (vector[i] / norm);
            }
            return normalized;
        }
    }

    /**
     * bce-embedding-base_v1模型生成的数据没有归一化，此方法可进行向量归一化。
     * 归一化是余弦相似度计算的重要前提，可以显著提高检索质量和稳定性。确保在整个流程中保持向量归一化的一致性。
     * 返回集合
     */
    public static List<Float> normalizeVectorList(RemoteEmbeddingModel remoteModel, String text) {
        final float[] vector = normalizeVector(remoteModel, text);
        final List<Float> normalized = new ArrayList<>(vector.length);
        for (float v : vector) {
            normalized.add(v);
        }
        return normalized;
    }

    /**
     * 生成摘要向量（分块处理，返回nested结构）
     */
    private List<PlospArticleEs.AbstractVector> generateAbstractVectors(String abstractText) {
        try {
            // 1. 清理HTML标签和规范化文本
            String cleanText = cleanAndNormalizeText(abstractText);

            if (StrUtil.isBlank(cleanText)) {
                return null;
            }

            // 2. 使用配置好的文本分割器分块
            List<Document> chunks = textSplitter.split(new Document(cleanText));

            List<PlospArticleEs.AbstractVector> vectors = new ArrayList<>();
            for (Document chunk : chunks) {
                assert chunk.getText() != null;
                String chunkText = chunk.getText().trim();

                if (StrUtil.isNotBlank(chunkText)) {
                    try {
                        float[] vector = normalizeVector(remoteEmbeddingModel, chunkText);
                        vectors.add(new PlospArticleEs.AbstractVector(vector));
                    } catch (Exception e) {
                        log.error("生成文本向量失败，文本长度：{}，错误：{}", chunkText.length(), e.getMessage());
                        // 跳过失败的文本块
                    }
                }
            }

            return vectors.isEmpty() ? null : vectors;

        } catch (Exception e) {
            log.error("生成摘要向量失败", e);
            return null;
        }
    }

    /**
     * 使用QueryWrapper查询影响因子、JCR分区和中科院分区信息
     */
    private void enrichArticlesWithJournalMetricsInfo(List<Article> articles) {
        try {
            if (CollUtil.isEmpty(articles)) {
                return;
            }

            // 1. 获取期刊ID列表
            List<Long> journalIds = articles.stream()
                    .map(Article::getJournalId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(journalIds)) {
                log.debug("没有期刊ID，跳过影响因子和分区信息查询");
                return;
            }

            // 2. 查询影响因子和JCR分区信息（取最新年份的数据）
            enrichArticlesWithIfAndJcrInfo(articles, journalIds);

            // 3. 查询中科院分区信息（取最新年份的数据）
            enrichArticlesWithZkySectionInfo(articles, journalIds);

        } catch (Exception e) {
            log.error("查询影响因子和分区信息失败", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 使用QueryWrapper查询影响因子和JCR分区信息
     */
    private void enrichArticlesWithIfAndJcrInfo(List<Article> articles, List<Long> journalIds) {
        try {
            // 查询影响因子表，获取所有相关数据
            QueryWrapper<TbDdsIfYear> ifYearWrapper = new QueryWrapper<>();
            ifYearWrapper.in("journal_id", journalIds);
            ifYearWrapper.select("journal_id", "year", "impact_factor", "jcr_quartile");
            List<TbDdsIfYear> ifYearList = tbDdsIfYearService.list(ifYearWrapper);

            if (CollUtil.isEmpty(ifYearList)) {
                log.debug("没有找到影响因子信息，期刊ID: {}", journalIds);
                return;
            }

            // 构建期刊ID到最新影响因子和JCR分区的映射
            Map<Long, String> journalIdToIfMap = new HashMap<>();
            Map<Long, String> journalIdToJcrMap = new HashMap<>();

            // 按期刊ID分组，每个期刊只取最新年份的数据
            Map<Long, List<TbDdsIfYear>> journalIfMap = ifYearList.stream()
                    .collect(Collectors.groupingBy(TbDdsIfYear::getJournalId));

            for (Map.Entry<Long, List<TbDdsIfYear>> entry : journalIfMap.entrySet()) {
                Long journalId = entry.getKey();
                List<TbDdsIfYear> ifYears = entry.getValue();

                // 对每个期刊的数据按年份降序排序，取最新年份的数据
                if (CollUtil.isNotEmpty(ifYears)) {
                    TbDdsIfYear latestIfYear = ifYears.stream()
                            .filter(ifYear -> StrUtil.isNotBlank(ifYear.getYear()))
                            .max((o1, o2) -> {
                                try {
                                    // 比较年份字符串，取最新的
                                    return o1.getYear().compareTo(o2.getYear());
                                } catch (Exception e) {
                                    log.warn("年份比较失败，期刊ID: {}, 年份1: {}, 年份2: {}",
                                            journalId, o1.getYear(), o2.getYear());
                                    return 0;
                                }
                            })
                            .orElse(null);

                    if (latestIfYear != null) {
                        if (StrUtil.isNotBlank(latestIfYear.getImpactFactor())) {
                            journalIdToIfMap.put(journalId, latestIfYear.getImpactFactor());
                        }

                        if (StrUtil.isNotBlank(latestIfYear.getJcrQuartile())) {
                            journalIdToJcrMap.put(journalId, latestIfYear.getJcrQuartile());
                        }
                    }
                }
            }

            // 将影响因子和JCR分区信息设置到Article对象中
            for (Article article : articles) {
                if (article.getJournalId() != null) {
                    Long journalId = article.getJournalId();

                    // 设置影响因子
                    String impactFactor = journalIdToIfMap.get(journalId);
                    if (StrUtil.isNotBlank(impactFactor)) {
                        try {
                            article.setImpactFactor(Float.parseFloat(impactFactor));
                        } catch (NumberFormatException e) {
                            log.warn("影响因子格式错误，期刊ID: {}, 影响因子: {}", journalId, impactFactor);
                            article.setImpactFactor(null);
                        }
                    }

                    // 设置JCR分区
                    String jcrQuartile = journalIdToJcrMap.get(journalId);
                    if (StrUtil.isNotBlank(jcrQuartile)) {
                        article.setJcr(jcrQuartile);
                    }
                }
            }

        } catch (Exception e) {
            log.error("查询影响因子和JCR分区信息失败", e);
        }
    }

    /**
     * 使用QueryWrapper查询中科院分区信息
     */
    private void enrichArticlesWithZkySectionInfo(List<Article> articles, List<Long> journalIds) {
        try {
            // 查询中科院分区表，获取所有相关数据
            QueryWrapper<TbDdsZkySection> zkySectionWrapper = new QueryWrapper<>();
            zkySectionWrapper.in("journal_id", journalIds);
            zkySectionWrapper.select("journal_id", "year", "large_category_section");
            List<TbDdsZkySection> zkySectionList = tbDdsZkySectionService.list(zkySectionWrapper);

            if (CollUtil.isEmpty(zkySectionList)) {
                log.debug("没有找到中科院分区信息，期刊ID: {}", journalIds);
                return;
            }

            // 构建期刊ID到最新中科院分区的映射
            Map<Long, Integer> journalIdToZkySectionMap = new HashMap<>();

            // 按期刊ID分组，每个期刊只取最新年份的数据
            Map<Long, List<TbDdsZkySection>> journalZkyMap = zkySectionList.stream()
                    .collect(Collectors.groupingBy(TbDdsZkySection::getJournalId));

            for (Map.Entry<Long, List<TbDdsZkySection>> entry : journalZkyMap.entrySet()) {
                Long journalId = entry.getKey();
                List<TbDdsZkySection> zkySections = entry.getValue();

                // 对每个期刊的数据按年份降序排序，取最新年份的数据
                if (CollUtil.isNotEmpty(zkySections)) {
                    TbDdsZkySection latestZkySection = zkySections.stream()
                            .filter(zkySection -> StrUtil.isNotBlank(zkySection.getYear()))
                            .max((o1, o2) -> {
                                try {
                                    // 比较年份字符串，取最新的
                                    return o1.getYear().compareTo(o2.getYear());
                                } catch (Exception e) {
                                    log.warn("年份比较失败，期刊ID: {}, 年份1: {}, 年份2: {}",
                                            journalId, o1.getYear(), o2.getYear());
                                    return 0;
                                }
                            })
                            .orElse(null);

                    if (latestZkySection != null && latestZkySection.getLargeCategorySection() != null) {
                        journalIdToZkySectionMap.put(journalId, latestZkySection.getLargeCategorySection());
                    }
                }
            }

            // 将中科院分区信息设置到Article对象中
            for (Article article : articles) {
                if (article.getJournalId() != null) {
                    Long journalId = article.getJournalId();

                    // 设置中科院分区
                    Integer largeCategorySection = journalIdToZkySectionMap.get(journalId);
                    if (largeCategorySection != null) {
                        article.setLargeCategorySection(largeCategorySection);
                    }
                }
            }

        } catch (Exception e) {
            log.error("查询中科院分区信息失败", e);
        }
    }

    /**
     * 初始化MeSH缓存 - 应用启动时调用
     */
    private void initMeshCache() {
        try {
            log.info("开始初始化MeSH缓存");

            // 检查是否已有缓存数据
            if (hasMeshCache()) {
                log.info("MeSH缓存已存在，跳过初始化");
                return;
            }

            // 加载数据到Redis
            int count = loadMeshDataToRedis();
            log.info("MeSH缓存初始化完成，共加载{}条数据", count);

        } catch (Exception e) {
            log.error("MeSH缓存初始化失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }

    /**
     * 将所有MeSH数据加载到Redis缓存中
     * 使用mesh_ui作为key，包含mesh_name、parent_info、child_info的Map作为value
     */
    public int loadMeshDataToRedis() {
        final long start = System.currentTimeMillis();
        log.info("开始加载MeSH数据到Redis缓存");

        try {
            // 清空现有缓存
            clearMeshCache();

            // 分批查询并处理MeSH数据
            int totalCount = processMeshDataInBatches();

            final long end = System.currentTimeMillis();
            log.info("MeSH数据加载完成，共处理: {}条数据，耗时: {}ms", totalCount, (end - start));

            return totalCount;

        } catch (Exception e) {
            log.error("加载MeSH数据到Redis失败", e);
            throw new RuntimeException("加载MeSH数据到Redis失败", e);
        }
    }

    /**
     * 分批处理MeSH数据并存入Redis
     */
    private int processMeshDataInBatches() {
        final AtomicInteger totalCount = new AtomicInteger(0);
        final AtomicInteger successCount = new AtomicInteger(0);
        final AtomicInteger failureCount = new AtomicInteger(0);

        log.info("开始分批处理MeSH数据，批次大小: {}", batchSize);

        // 使用游标分页，避免大offset问题
        Long lastId = 0L;
        int batchIndex = 0;

        while (true) {
            batchIndex++;
            final int currentBatchIndex = batchIndex;

            try {
                long batchStart = System.currentTimeMillis();

                // 使用QueryWrapper查询 - 游标分页，只查询需要的字段
                QueryWrapper<TbDdsMesh> queryWrapper = new QueryWrapper<>();
                if (lastId != null) {
                    queryWrapper.gt("id", lastId);
                }
                queryWrapper.select("id", "mesh_ui", "mesh_name", "parent_info", "child_info");
                queryWrapper.orderByAsc("id");
                queryWrapper.last("LIMIT " + batchSize);

                List<TbDdsMesh> meshList = tbDdsMeshMapper.selectList(queryWrapper);

                if (meshList.isEmpty()) {
                    log.info("MeSH数据游标分页查询完成，共处理{}个批次", batchIndex - 1);
                    break;
                }

                // 更新游标
                lastId = meshList.get(meshList.size() - 1).getId();

                // 处理单个批次
                int batchSuccessCount = processMeshBatch(meshList, currentBatchIndex);

                long batchEnd = System.currentTimeMillis();
                long batchTime = batchEnd - batchStart;

                // 更新统计信息
                totalCount.addAndGet(meshList.size());
                successCount.addAndGet(batchSuccessCount);
                failureCount.addAndGet(meshList.size() - batchSuccessCount);

                log.info("MeSH批次[{}]完成，耗时: {}ms, 查询: {}, 成功: {}, 失败: {}, 已处理: {}条, 当前游标ID: {}",
                        currentBatchIndex, batchTime, meshList.size(),
                        batchSuccessCount, meshList.size() - batchSuccessCount, totalCount.get(), lastId);

            } catch (Exception e) {
                log.error("MeSH批次[{}]处理失败", currentBatchIndex, e);
                failureCount.addAndGet(batchSize);

                // 如果是数据库连接问题，稍作等待后继续
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        log.info("MeSH数据处理完成统计 - 总数: {}, 成功: {}, 失败: {}",
                totalCount.get(), successCount.get(), failureCount.get());

        return totalCount.get();
    }

    /**
     * 处理单个MeSH批次数据
     */
    private int processMeshBatch(List<TbDdsMesh> meshList, int batchIndex) {
        int successCount = 0;

        try {
            redisTemplate.executePipelined(new SessionCallback<Object>() {

                @Override
                public Object execute(RedisOperations operations) throws DataAccessException {
                    for (TbDdsMesh mesh : meshList) {
                        try {
                            // 构建Redis value
                            Map<String, String> meshInfo = new HashMap<>();
                            meshInfo.put("mesh_name", mesh.getMeshName());
                            meshInfo.put("parent_info", mesh.getParentInfo());
                            meshInfo.put("child_info", mesh.getChildInfo());

                            // 使用高级API
                            String redisKey = "mesh:" + mesh.getMeshUi();
                            operations.opsForHash().putAll(redisKey, meshInfo);

                            // 建立搜索索引：只使用Hash进行反向映射（兼容性最好）
                            String meshNameLower = mesh.getMeshName().toLowerCase();
                            operations.opsForHash().put("mesh_name_to_ui", meshNameLower, mesh.getMeshUi());

                        } catch (Exception e) {
                            log.error("MeSH批次[{}]单条数据准备失败，mesh_ui: {}", batchIndex, mesh.getMeshUi(), e);
                        }
                    }
                    return null;
                }
            });
        } catch (Exception e) {
            log.error("MeSH批次[{}]处理异常", batchIndex, e);
        }

        return successCount;
    }

    /**
     * 根据mesh_ui从Redis获取MeSH信息
     */
    public Map<String, String> getMeshInfoFromRedis(String meshUi) {
        try {
            if (StrUtil.isBlank(meshUi)) {
                return null;
            }

            String redisKey = "mesh:" + meshUi;
            Map<Object, Object> rawMap = redisTemplate.opsForHash().entries(redisKey);

            if (rawMap.isEmpty()) {
                return null;
            }

            // 转换为String类型的Map
            Map<String, String> meshInfo = new HashMap<>();
            rawMap.forEach((key, value) -> {
                if (key != null && value != null) {
                    meshInfo.put(key.toString(), value.toString());
                }
            });

            return meshInfo;

        } catch (Exception e) {
            log.error("从Redis获取MeSH信息失败，mesh_ui: {}", meshUi, e);
            return null;
        }
    }

    /**
     * 清空Redis中的MeSH缓存数据 - 同步删除避免竞态条件
     */
    public void clearMeshCache() {
        try {
            log.info("开始清空Redis中的MeSH缓存数据");

            // 1. 删除搜索索引
            redisTemplate.delete("mesh_names_zset");
            redisTemplate.delete("mesh_name_to_ui");

            // 2. 同步批量删除所有mesh:*的数据（避免竞态条件）
            int batchSize = 5000;

            redisTemplate.execute((RedisCallback<Void>) connection -> {
                ScanOptions options = ScanOptions.scanOptions()
                        .match("mesh:*")
                        .count(batchSize)
                        .build();

                try (Cursor<byte[]> cursor = connection.scan(options)) {
                    List<String> keysToDelete = new ArrayList<>();


                    while (cursor.hasNext()) {
                        byte[] key = cursor.next();
                        String keyStr = new String(key);
                        keysToDelete.add(keyStr);

                        // 当积累到一批时，同步批量删除
                        if (keysToDelete.size() >= batchSize) {
                            String[] keyArray = keysToDelete.toArray(new String[0]);
                            redisTemplate.delete(List.of(keyArray));
                            keysToDelete.clear();
                        }
                    }

                    // 删除剩余的key
                    if (!keysToDelete.isEmpty()) {
                        String[] keyArray = keysToDelete.toArray(new String[0]);
                        redisTemplate.delete(List.of(keyArray));
                    }

                } catch (Exception e) {
                    log.error("扫描删除key失败", e);
                }
                return null;
            });

            log.info("清空MeSH缓存完成");

        } catch (Exception e) {
            log.error("清空MeSH缓存失败", e);
        }
    }

    /**
     * 检查Redis中是否存在MeSH缓存数据
     */
    public boolean hasMeshCache() {
        try {
            AtomicBoolean hasKeys = new AtomicBoolean(false);

            redisTemplate.execute((RedisCallback<Void>) connection -> {
                ScanOptions options = ScanOptions.scanOptions()
                        .match("mesh:*")
                        .count(1)
                        .build();

                try (Cursor<byte[]> cursor = connection.scan(options)) {
                    if (cursor.hasNext()) {
                        hasKeys.set(true);
                    }
                } catch (Exception e) {
                    log.error("扫描检查key失败", e);
                }
                return null;
            });

            return hasKeys.get();

        } catch (Exception e) {
            log.error("检查MeSH缓存状态失败", e);
            return false;
        }
    }

    /**
     * MeSH词汇自动完成搜索 - 全量搜索版本
     * 保证扫描所有35万条数据，不遗漏任何匹配结果
     */
    public List<Map<String, String>> searchMeshByName(String keyword) {
        try {
            if (StrUtil.isBlank(keyword) || keyword.trim().length() < 2) {
                return new ArrayList<>();
            }

            String searchKeyword = keyword.trim().toLowerCase();
            int maxSuggestions = 10;

            // 策略1：优先使用Hash索引进行快速搜索
            List<Map<String, String>> results = searchMeshByHashIndex(searchKeyword, maxSuggestions);
            if (!results.isEmpty()) {
                return results;
            }

            // 策略2：Hash索引失败时，进行全量扫描（保证覆盖所有数据）
            return searchMeshByFullScan(searchKeyword, maxSuggestions);

        } catch (Exception e) {
            log.error("MeSH搜索失败，关键词: {}", keyword, e);
            return new ArrayList<>();
        }
    }

    /**
     * 使用Redis Hash索引进行高速搜索
     * 优先使用mesh_name_to_ui索引，速度最快
     */
    private List<Map<String, String>> searchMeshByHashIndex(String searchKeyword, int maxSuggestions) {
        List<Map<String, String>> suggestions = new ArrayList<>();

        try {
            // 检查Hash索引是否存在
            if (!redisTemplate.hasKey("mesh_name_to_ui")) {
                log.debug("Hash索引不存在，跳过Hash搜索");
                return suggestions;
            }

            // 从mesh_name_to_ui Hash中获取所有键值对
            Map<Object, Object> allMeshNames = redisTemplate.opsForHash().entries("mesh_name_to_ui");

            if (!allMeshNames.isEmpty()) {
                log.debug("Hash索引包含{}条记录，开始搜索关键词: {}", allMeshNames.size(), searchKeyword);

                // 在内存中进行高效搜索
                for (Map.Entry<Object, Object> entry : allMeshNames.entrySet()) {
                    if (suggestions.size() >= maxSuggestions) break;

                    String meshNameLower = entry.getKey().toString();
                    String meshUi = entry.getValue().toString();

                    // 检查是否匹配搜索关键词
                    if (meshNameLower.contains(searchKeyword)) {
                        // 获取原始的mesh_name（大小写正确的）
                        String redisKey = "mesh:" + meshUi;
                        Object meshNameObj = redisTemplate.opsForHash().get(redisKey, "mesh_name");

                        if (meshNameObj != null) {
                            String originalMeshName = meshNameObj.toString();
                            Map<String, String> suggestion = new HashMap<>();
                            suggestion.put("meshName", originalMeshName);
                            suggestion.put("meshUi", meshUi);
                            suggestions.add(suggestion);
                        }
                    }
                }

                // 按mesh_name排序
                suggestions.sort((a, b) -> a.get("meshName").compareToIgnoreCase(b.get("meshName")));

                log.debug("Hash索引搜索完成，关键词: {}, 找到: {}条匹配", searchKeyword, suggestions.size());
            }

        } catch (Exception e) {
            log.error("Hash索引搜索失败: {}", e.getMessage(), e);
        }

        return suggestions;
    }

    /**
     * 全量扫描所有MeSH数据
     * 使用多线程并行处理
     */
    private List<Map<String, String>> searchMeshByFullScan(String searchKeyword, int maxSuggestions) {
        List<Map<String, String>> suggestions = Collections.synchronizedList(new ArrayList<>());

        try {
            log.info("开始全量扫描所有MeSH数据，关键词: {}", searchKeyword);

            // 使用现有的线程池，避免创建新的线程池
            // 如果现有线程池繁忙，创建专用的搜索线程池
            ThreadPoolExecutor executor;
            boolean useExistingPool = false;

            if (threadPoolExecutor != null && !threadPoolExecutor.isShutdown() &&
                    threadPoolExecutor.getActiveCount() < threadPoolExecutor.getCorePoolSize()) {
                executor = threadPoolExecutor;
                useExistingPool = true;
                log.debug("使用现有线程池进行MeSH搜索");
            } else {
                // 创建专用的搜索线程池 - 使用16线程进行高并发处理
                int coreThreads = 16;
                int maxThreads = 24;
                executor = new ThreadPoolExecutor(
                        coreThreads, maxThreads, 60L, TimeUnit.SECONDS,
                        new LinkedBlockingQueue<>(2000),
                        new ThreadFactory() {
                            private final AtomicInteger threadNumber = new AtomicInteger(1);

                            @Override
                            public Thread newThread(Runnable r) {
                                Thread t = new Thread(r, "mesh-search-" + threadNumber.getAndIncrement());
                                t.setDaemon(false);
                                t.setUncaughtExceptionHandler((thread, ex) -> {
                                    log.error("MeSH搜索线程 {} 发生未捕获异常", thread.getName(), ex);
                                });
                                return t;
                            }
                        },
                        new ThreadPoolExecutor.AbortPolicy()
                );
                log.debug("创建专用MeSH搜索线程池: core={}, max={}", coreThreads, maxThreads);
            }

            // 分批扫描参数 - 减小批次大小以增加并发度
            final int batchSize = 5000;
            final AtomicInteger totalScanned = new AtomicInteger(0);
            final AtomicInteger totalMatched = new AtomicInteger(0);
            final AtomicInteger activeTasks = new AtomicInteger(0);
            final AtomicBoolean shouldStop = new AtomicBoolean(false);

            // 使用CountDownLatch确保所有扫描和处理任务完成
            int scanThreadCount = 4;
            CountDownLatch scanLatch = new CountDownLatch(scanThreadCount);

            // 启动多个扫描线程，每个线程使用不同的游标位置
            for (int threadIndex = 0; threadIndex < scanThreadCount; threadIndex++) {
                final int currentThreadIndex = threadIndex;
                final int totalScanThreads = scanThreadCount;

                executor.submit(() -> {
                    try {
                        redisTemplate.execute((RedisCallback<Void>) connection -> {
                            ScanOptions options = ScanOptions.scanOptions()
                                    .match("mesh:*")
                                    .count(batchSize * 2)
                                    .build();

                            try (Cursor<byte[]> cursor = connection.scan(options)) {
                                List<String> currentBatch = new ArrayList<>();
                                int keyIndex = 0;

                                // 智能扫描：找到足够结果时停止，否则扫描全部
                                while (cursor.hasNext() && !shouldStop.get()) {
                                    byte[] keyBytes = cursor.next();

                                    // 线程分片：每个扫描线程只处理属于自己的数据片段
                                    if (keyIndex % totalScanThreads == currentThreadIndex) {
                                        String key = new String(keyBytes);
                                        currentBatch.add(key);

                                        // 当积累到一批时，提交处理任务
                                        if (currentBatch.size() >= batchSize) {
                                            List<String> batchToProcess = new ArrayList<>(currentBatch);
                                            activeTasks.incrementAndGet();
                                            executor.submit(() -> {
                                                try {
                                                    processBatchForFullScan(batchToProcess, searchKeyword, suggestions, maxSuggestions, totalScanned, totalMatched, shouldStop);
                                                } finally {
                                                    activeTasks.decrementAndGet();
                                                }
                                            });
                                            currentBatch.clear();
                                        }
                                    }
                                    keyIndex++;
                                }

                                // 处理最后一批
                                if (!currentBatch.isEmpty()) {
                                    List<String> finalBatch = new ArrayList<>(currentBatch);
                                    activeTasks.incrementAndGet();
                                    executor.submit(() -> {
                                        try {
                                            processBatchForFullScan(finalBatch, searchKeyword, suggestions, maxSuggestions, totalScanned, totalMatched, shouldStop);
                                        } finally {
                                            activeTasks.decrementAndGet();
                                        }
                                    });
                                }

                            } catch (Exception e) {
                                log.error("扫描线程{}发生错误", currentThreadIndex, e);
                            }

                            return null;
                        });

                        log.debug("扫描线程{}完成", currentThreadIndex);

                    } catch (Exception e) {
                        log.error("扫描线程{}失败", currentThreadIndex, e);
                    } finally {
                        scanLatch.countDown();
                    }
                });
            }

            // 等待所有扫描线程完成
            boolean completed = scanLatch.await(60, TimeUnit.SECONDS);
            if (!completed) {
                log.warn("扫描线程超时，当前已找到{}条匹配", suggestions.size());
            } else {
                log.info("所有扫描线程完成，等待处理任务完成...");
            }

            // 等待所有处理任务完成或找到足够结果
            int waitCount = 0;
            while (activeTasks.get() > 0 && waitCount < 600 && !shouldStop.get()) {
                try {
                    Thread.sleep(100);
                    waitCount++;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            // 只有新创建的线程池才需要关闭
            if (!useExistingPool) {
                executor.shutdown();
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("处理任务未能在30秒内完成，强制关闭线程池");
                    executor.shutdownNow();
                }
            }

            // 按mesh_name排序
            List<Map<String, String>> sortedResults = new ArrayList<>(suggestions);
            sortedResults.sort((a, b) -> a.get("meshName").compareToIgnoreCase(b.get("meshName")));

            return sortedResults.subList(0, Math.min(sortedResults.size(), maxSuggestions));

        } catch (Exception e) {
            log.error("全量扫描失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理一批keys的全量扫描
     */
    private void processBatchForFullScan(List<String> keys, String searchKeyword,
                                         List<Map<String, String>> suggestions, int maxSuggestions,
                                         AtomicInteger totalScanned, AtomicInteger totalMatched,
                                         AtomicBoolean shouldStop) {
        if (keys.isEmpty() || shouldStop.get()) {
            return;
        }

        try {
            // Pipeline查询这批keys的mesh_name
            List<Object> meshNames = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (String key : keys) {
                    connection.hashCommands().hGet(key.getBytes(), "mesh_name".getBytes());
                }
                return null;
            });

            totalScanned.addAndGet(keys.size());

            // 检查匹配 - 扫描当前批次的所有数据
            for (int i = 0; i < keys.size() && !shouldStop.get(); i++) {
                Object meshNameObj = meshNames.get(i);
                if (meshNameObj != null) {
                    String meshName = meshNameObj.toString();
                    if (meshName.toLowerCase().contains(searchKeyword)) {
                        totalMatched.incrementAndGet();

                        // 线程安全地添加结果
                        synchronized (suggestions) {
                            if (suggestions.size() < maxSuggestions) {
                                Map<String, String> suggestion = new HashMap<>();
                                suggestion.put("meshName", meshName);
                                suggestion.put("meshUi", keys.get(i).replace("mesh:", ""));
                                suggestions.add(suggestion);

                                // 如果找到足够的结果，设置停止标志
                                if (suggestions.size() >= maxSuggestions) {
                                    shouldStop.set(true);
                                    break;
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("处理批次失败", e);
        }
    }

    /**
     * 多线程处理批次中所有文章的MeSH数据（分批提交避免线程池溢出）
     *
     * @param esArticles ES文章实体列表
     * @param batchIndex 批次索引
     */
    private void processMeshDataAsync(List<PlospArticleEs> esArticles, int batchIndex) {
        // 每次最多提交50个任务到线程池
        int subBatchSize = 50;
        List<List<PlospArticleEs>> subBatches = new ArrayList<>();

        // 将文章列表分成小批次
        for (int i = 0; i < esArticles.size(); i += subBatchSize) {
            int endIndex = Math.min(i + subBatchSize, esArticles.size());
            subBatches.add(esArticles.subList(i, endIndex));
        }

        log.debug("批次[{}]MeSH处理分为{}个子批次，每批最多{}篇文章", batchIndex, subBatches.size(), subBatchSize);

        // 逐个处理子批次
        for (int subBatchIndex = 0; subBatchIndex < subBatches.size(); subBatchIndex++) {
            List<PlospArticleEs> subBatch = subBatches.get(subBatchIndex);
            List<CompletableFuture<Void>> meshFutures = new ArrayList<>();

            // 提交当前子批次的任务
            for (PlospArticleEs esArticle : subBatch) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        processMeshData(esArticle, esArticle.getId());
                    } catch (Exception e) {
                        log.error("处理文章MeSH数据失败，ID: {}, 批次: {}", esArticle.getId(), batchIndex, e);
                    }
                }, threadPoolExecutor);
                meshFutures.add(future);
            }

            // 等待当前子批次完成
            try {
                CompletableFuture.allOf(meshFutures.toArray(new CompletableFuture[0]))
                        .get(300, TimeUnit.SECONDS);
                log.debug("批次[{}]子批次[{}]MeSH处理完成，处理{}篇文章", batchIndex, subBatchIndex + 1, subBatch.size());
            } catch (TimeoutException e) {
                log.error("批次[{}]子批次[{}]MeSH数据处理超时", batchIndex, subBatchIndex + 1, e);
            } catch (Exception e) {
                log.error("批次[{}]子批次[{}]MeSH数据处理异常", batchIndex, subBatchIndex + 1, e);
            }
        }

        log.debug("批次[{}]所有MeSH数据处理完成，共{}篇文章", batchIndex, esArticles.size());
    }

    /**
     * 处理文章的MeSH数据，查询关联的MeSH信息并递归获取父子关系
     *
     * @param esArticle ES文章实体
     * @param articleId 文章ID
     */
    public void processMeshData(PlospArticleEs esArticle, Long articleId) {
        try {
            if (articleId == null) {
                return;
            }

            // 查询文章关联的MeSH数据
            List<String> allMeshUis = queryArticleMeshData(articleId);

            if (allMeshUis.isEmpty()) {
                log.debug("文章ID: {} 没有关联的MeSH数据", articleId);
                return;
            }

            // 递归查询父子关系并去重
            Set<String> finalMeshUis = recursiveQueryMeshHierarchy(allMeshUis);

            // 转换为List并存入ES
            List<String> meshUiList = new ArrayList<>(finalMeshUis);
            esArticle.setMeshUi(CollUtil.isEmpty(meshUiList) ? null : meshUiList);

            log.debug("文章ID: {} 处理MeSH数据完成，共{}个meshUi", articleId, meshUiList.size());

        } catch (Exception e) {
            log.error("处理文章MeSH数据失败，文章ID: {}", articleId, e);
            // 不抛出异常，避免影响整个文章处理流程
        }
    }

    /**
     * 查询文章关联的MeSH数据
     */
    private List<String> queryArticleMeshData(Long articleId) {
        try {
            // 使用QueryWrapper查询指定字段
            QueryWrapper<ArticleMesh> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("doc_id", articleId);
            queryWrapper.select("descriptor_id", "qualifier_id1", "qualifier_id2", "qualifier_id3");

            List<ArticleMesh> meshList = articleMeshMapper.selectList(queryWrapper);

            // 收集所有非空的meshUi，使用Set去重
            Set<String> meshUiSet = new HashSet<>();
            for (ArticleMesh mesh : meshList) {
                addMeshUiIfNotEmpty(meshUiSet, mesh.getDescriptorId());
                addMeshUiIfNotEmpty(meshUiSet, mesh.getQualifierId1());
                addMeshUiIfNotEmpty(meshUiSet, mesh.getQualifierId2());
                addMeshUiIfNotEmpty(meshUiSet, mesh.getQualifierId3());
            }

            List<String> meshUis = new ArrayList<>(meshUiSet);
            log.debug("文章ID: {} 查询到{}条MeSH关联记录，收集到{}个去重后meshUi",
                    articleId, meshList.size(), meshUis.size());

            return meshUis;

        } catch (Exception e) {
            log.error("查询文章MeSH关联数据失败，文章ID: {}", articleId, e);
            return new ArrayList<String>();
        }
    }

    /**
     * 添加非空的meshUi到集合中
     */
    private void addMeshUiIfNotEmpty(Set<String> meshUis, String meshUi) {
        if (StrUtil.isNotBlank(meshUi)) {
            meshUis.add(meshUi.trim());
        }
    }

    /**
     * 递归查询MeSH层次结构，获取所有父子关系的meshUi（批量查询优化版本）
     */
    private Set<String> recursiveQueryMeshHierarchy(List<String> initialMeshUis) {
        Set<String> allMeshUis = new HashSet<>();
        Set<String> processedMeshUis = new HashSet<>();
        List<String> currentBatch = new ArrayList<>(initialMeshUis);

        int maxBatches = 100;
        int batchCount = 0;
        int batchSize = 50;

        while (!currentBatch.isEmpty() && batchCount < maxBatches) {
            batchCount++;

            // 过滤掉已处理的meshUi
            currentBatch = currentBatch.stream()
                    .filter(meshUi -> !processedMeshUis.contains(meshUi))
                    .distinct()
                    .collect(Collectors.toList());

            if (currentBatch.isEmpty()) {
                break;
            }

            // 批量查询当前批次的MeSH信息
            List<String> nextBatch = batchQueryMeshHierarchy(currentBatch, batchSize);

            // 更新已处理和所有meshUis
            allMeshUis.addAll(currentBatch);
            processedMeshUis.addAll(currentBatch);

            // 准备下一批次
            currentBatch = nextBatch;
        }

        if (batchCount >= maxBatches) {
            log.warn("MeSH层次结构查询达到最大批次限制: {}", maxBatches);
        }

        log.debug("MeSH层次结构查询完成，初始{}个，最终{}个，批次{}次",
                initialMeshUis.size(), allMeshUis.size(), batchCount);

        return allMeshUis;
    }

    /**
     * 批量查询MeSH层次结构信息
     */
    private List<String> batchQueryMeshHierarchy(List<String> meshUis, int batchSize) {
        List<String> allNextMeshUis = new ArrayList<>();

        // 分批处理，避免单次查询过多
        for (int i = 0; i < meshUis.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, meshUis.size());
            List<String> subBatch = meshUis.subList(i, endIndex);

            try {
                List<String> nextMeshUis = batchQuerySingleBatch(subBatch);
                allNextMeshUis.addAll(nextMeshUis);
            } catch (Exception e) {
                log.error("批量查询MeSH信息失败，批次大小: {}", subBatch.size(), e);
                // 失败时降级为单个查询
                List<String> fallbackResult = fallbackToSingleQuery(subBatch);
                allNextMeshUis.addAll(fallbackResult);
            }
        }

        return allNextMeshUis;
    }

    /**
     * 批量查询单个批次
     */
    private List<String> batchQuerySingleBatch(List<String> meshUis) {
        List<String> nextMeshUis = new ArrayList<>();

        try {
            // 使用Pipeline批量查询Hash，提高效率
            List<Object> results = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (String meshUi : meshUis) {
                    String redisKey = "mesh:" + meshUi;
                    connection.hashCommands().hGetAll(redisKey.getBytes());
                }
                return null;
            });

            // 处理Pipeline结果
            for (int i = 0; i < results.size(); i++) {
                Object result = results.get(i);
                if (result instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<Object, Object> rawMap = (Map<Object, Object>) result;

                    if (!rawMap.isEmpty()) {
                        // 转换为String类型的Map
                        Map<String, String> meshInfo = new HashMap<>();
                        rawMap.forEach((key, value) -> {
                            if (key != null && value != null) {
                                meshInfo.put(key.toString(), value.toString());
                            }
                        });

                        // 解析parent_info和child_info
                        List<String> parentChildMeshUis = parseMeshHierarchyInfo(meshInfo);
                        nextMeshUis.addAll(parentChildMeshUis);
                    }
                }
            }

        } catch (Exception e) {
            log.error("批量查询Redis失败", e);
            throw e;
        }

        // 对本批次结果去重
        return nextMeshUis.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 降级为单个查询（当批量查询失败时）
     */
    private List<String> fallbackToSingleQuery(List<String> meshUis) {
        List<String> nextMeshUis = new ArrayList<>();

        for (String meshUi : meshUis) {
            try {
                Map<String, String> meshInfo = getMeshInfoFromRedis(meshUi);
                if (meshInfo != null) {
                    List<String> parentChildMeshUis = parseMeshHierarchyInfo(meshInfo);
                    nextMeshUis.addAll(parentChildMeshUis);
                }
            } catch (Exception e) {
                log.error("单个查询MeSH信息失败，meshUi: {}", meshUi, e);
            }
        }

        // 降级查询也需要去重
        return nextMeshUis.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 解析MeSH的parent_info和child_info字段，提取其中的meshUi
     */
    private List<String> parseMeshHierarchyInfo(Map<String, String> meshInfo) {
        Set<String> meshUiSet = new HashSet<>();

        try {
            String parentInfo = meshInfo.get("parent_info");
            String childInfo = meshInfo.get("child_info");

            // 解析parent_info
            if (StrUtil.isNotBlank(parentInfo)) {
                List<String> parentMeshUis = extractMeshUisFromInfo(parentInfo);
                meshUiSet.addAll(parentMeshUis);
            }

            // 解析child_info
            if (StrUtil.isNotBlank(childInfo)) {
                List<String> childMeshUis = extractMeshUisFromInfo(childInfo);
                meshUiSet.addAll(childMeshUis);
            }

        } catch (Exception e) {
            log.error("解析MeSH层次信息失败", e);
        }

        return new ArrayList<>(meshUiSet);
    }

    /**
     * 从parent_info或child_info字符串中提取meshUi
     * 这里需要根据实际数据格式进行解析
     */
    private List<String> extractMeshUisFromInfo(String info) {
        Set<String> meshUiSet = new HashSet<>();

        if (StrUtil.isBlank(info)) {
            return new ArrayList<String>();
        }

        try {
            // 假设数据格式可能是：
            // 1. 逗号分隔：D000001,D000002,D000003
            // 2. JSON格式：["D000001","D000002"]
            // 3. 其他分隔符格式

            // 先尝试按逗号分隔
            if (info.contains(",")) {
                String[] parts = info.split(",");
                for (String part : parts) {
                    String trimmed = part.trim();
                    if (StrUtil.isNotBlank(trimmed) && isMeshUiFormat(trimmed)) {
                        meshUiSet.add(trimmed);
                    }
                }
            }
            // 尝试JSON格式解析
            else if (info.startsWith("[") && info.endsWith("]")) {
                // 简单的JSON数组解析，去掉括号和引号
                String content = info.substring(1, info.length() - 1);
                String[] parts = content.split(",");
                for (String part : parts) {
                    String trimmed = part.trim().replaceAll("\"", "");
                    if (StrUtil.isNotBlank(trimmed) && isMeshUiFormat(trimmed)) {
                        meshUiSet.add(trimmed);
                    }
                }
            }
            // 单个值
            else if (isMeshUiFormat(info.trim())) {
                meshUiSet.add(info.trim());
            }

        } catch (Exception e) {
            log.error("提取MeSH UI失败，info: {}", info, e);
        }

        return new ArrayList<>(meshUiSet);
    }

    /**
     * 检查字符串是否符合MeSH UI格式（通常以D开头）
     */
    private boolean isMeshUiFormat(String str) {
        if (StrUtil.isBlank(str)) {
            return false;
        }
        // MeSH UI通常格式为：D000001, C000001等
        return str.matches("^[A-Z]\\d{6}$");
    }

    /**
     * 增强的文本清理和规范化方法
     * 用于处理HTML标签、特殊字符和长度限制
     */
    private String cleanAndNormalizeText(String text) {
        if (StrUtil.isBlank(text)) {
            return text;
        }

        try {
            // 1. 清理HTML标签
            String cleanText = HtmlUtil.cleanHtmlTag(text);

            // 2. 清理HTML实体
            cleanText = cleanText.replaceAll("&nbsp;", " ")
                    .replaceAll("&amp;", "&")
                    .replaceAll("&lt;", "<")
                    .replaceAll("&gt;", ">")
                    .replaceAll("&quot;", "\"")
                    .replaceAll("&apos;", "'")
                    .replaceAll("&#xa0;", " ")
                    .replaceAll("&#\\d+;", " ");

            // 3. 清理多余的空白字符
            cleanText = cleanText.replaceAll("\\s+", " ").trim();

            // 4. 移除可能导致解析问题的特殊字符
            cleanText = cleanText.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");

            // 5. 限制长度以避免keyword_lower字段解析问题
            if (cleanText.length() > 1000000) {
                cleanText = cleanText.substring(0, 1000000) + "...";
                log.debug("文本内容过长，已截断到1000000字符");
            }

            return cleanText;

        } catch (Exception e) {
            log.error("文本清理失败，返回原始文本", e);
            // 如果清理失败，返回截断的原始文本
            return text.length() > 1000000 ? text.substring(0, 1000000) + "..." : text;
        }
    }

    /**
     * 基于标题向量执行KNN相似度查询
     * @param titleVector 标题向量
     * @param size 返回结果数量
     * @param excludeId 排除的文章ID（当前文章）
     * @return 相似文章列表
     */
    public List<PlospArticleEs> findSimilarArticlesByTitleVector(List<Float> titleVector, int size, Long excludeId) {
        try {
            // 构建KNN查询
            Query knnQuery = QueryBuilders.knn()
                    .field("title_vector")
                    .queryVector(titleVector)
                    .k(size + 1) // 多查询一个，用于排除当前文章
                    .numCandidates(size * 10)
                    .build()._toQuery();

            // 如果需要排除当前文章，添加过滤条件
            Query finalQuery = knnQuery;
            if (excludeId != null) {
                finalQuery = QueryBuilders.bool()
                        .must(knnQuery)
                        .mustNot(QueryBuilders.term().field("id").value(excludeId).build()._toQuery())
                        .build()._toQuery();
            }

            // 执行查询
            NativeQueryBuilder queryBuilder = new NativeQueryBuilder()
                    .withQuery(finalQuery)
                    .withMaxResults(size);

            SearchHits<PlospArticleEs> searchHits = elasticsearchTemplate.search(
                    queryBuilder.build(), PlospArticleEs.class);

            return searchHits.getSearchHits().stream()
                    .map(SearchHit::getContent)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("执行KNN相似度查询失败", e);
            return Collections.emptyList();
        }
    }
}
