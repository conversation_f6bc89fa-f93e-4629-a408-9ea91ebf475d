package org.biosino.lf.plosp.portal.web.service;

import org.biosino.lf.plosp.portal.web.vo.ArticleDetailVO;
import org.biosino.lf.plosp.portal.web.vo.ReferenceResult;

/**
 * <AUTHOR>
 */
public interface ArticleDetailService {

    /**
     * 获取文章详情
     */
    ArticleDetailVO getArticleDetail(String id);

    /**
     * 文档解析
     */
    String getOrCreateInterpretation(Long docId);

    /**
     * 查找参考文档
     */
    ReferenceResult getReferences(Long docId, Integer limit);

}
