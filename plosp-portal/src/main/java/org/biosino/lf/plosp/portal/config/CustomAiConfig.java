package org.biosino.lf.plosp.portal.config;

import org.springframework.ai.transformer.splitter.TextSplitter;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/3/12
 */
@Configuration
public class CustomAiConfig {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /*@Bean("remoteEmbeddingModel")
    public RemoteEmbeddingModel remoteEmbeddingModel(RemoteEmbeddingService remoteEmbeddingService) {
        return new RemoteEmbeddingModel(remoteEmbeddingService);
    }*/

    @Bean
    public TextSplitter textSplitter(Environment environment) throws IOException {
        final String maxLengthKey = "spring.ai.embedding.transformer.tokenizer.options.maxLength";
        final String maxLengthStr = environment.getProperty(maxLengthKey);
        int maxLength = 512;
        if (maxLengthStr != null) {
            maxLength = Integer.parseInt(maxLengthStr);
        }

        // TokenTextSplitter 参数说明：
        // chunkSize: 每个文本块的目标token数量
        // minChunkSizeChars: 每个文本块的最小字符数
        // minChunkLengthToEmbed: 丢弃短于此长度的块
        // maxNumChunks: 从文本生成的最大块数
        // keepSeparator: 是否保留分隔符

        // 安全配置：确保不超过模型限制
        int chunkSize = maxLength - 10; // 留出10个token的安全边距
        int minChunkSizeChars = 100;    // 最小100个字符
        int minChunkLengthToEmbed = 10; // 最小10个字符才进行嵌入
        int maxNumChunks = 5000;        // 最多5000个块
        boolean keepSeparator = true;   // 保留分隔符

        // 按 Token 数量分块
        return new TokenTextSplitter(chunkSize, minChunkSizeChars, minChunkLengthToEmbed, maxNumChunks, keepSeparator);
    }

}
