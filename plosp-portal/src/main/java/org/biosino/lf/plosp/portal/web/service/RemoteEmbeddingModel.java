package org.biosino.lf.plosp.portal.web.service;

import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.plosp.portal.es.service.RemoteEmbeddingService;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

/**
 * 基于远程服务的向量化模型包装类
 * 当配置spring.ai.embedding.transformer.enabled为true时，调用本地模型
 * 简单包装，不实现复杂的 Spring AI 接口
 *
 * <AUTHOR>
 */

@Slf4j
@Service("remoteEmbeddingModel")
public class RemoteEmbeddingModel {
    private final RemoteEmbeddingService remoteEmbeddingService;
    private final EmbeddingModel embeddingModel;
    private final boolean enabledLocalModel;

    public RemoteEmbeddingModel(RemoteEmbeddingService remoteEmbeddingService, EmbeddingModel embeddingModel, Environment environment) {
        this.remoteEmbeddingService = remoteEmbeddingService;
        this.embeddingModel = embeddingModel;
        this.enabledLocalModel = "true".equalsIgnoreCase(environment.getProperty("spring.ai.embedding.transformer.enabled"));
    }

    /**
     * 单个文本向量化，返回 float[]
     */
    public float[] embed(String text) {
        if (enabledLocalModel) {
            return embedWithLocalModel(text);
        } else {
            return remoteEmbeddingService.embed(text);
        }
    }

    /**
     * 不要假设 EmbeddingModel.embed() 在 Spring AI 的 ONNX 实现下是线程安全的。社区已有并发相关的 bug 报告。
     * <a href="https://github.com/spring-projects/spring-ai/issues/2152?utm_source=chatgpt.com">...</a>
     */
    private synchronized float[] embedWithLocalModel(String text) {
        return embeddingModel.embed(text);
    }

}
