package org.biosino.lf.plosp.portal.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ArticleDetailVO {

    /**
     * 文章ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 自定义ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long customId;

    /**
     * 标题
     */
    private String title;

    /**
     * PMID
     */
    private Long pmid;

    /**
     * PMCID
     */
    private Long pmcId;

    /**
     * DOI
     */
    private String doi;

    /**
     * 下载量
     */
    private Integer download;

    /**
     * 访问量
     */
    private Integer hitNum;

    /**
     * 来源
     */
    private List<String> source;

    /**
     * 期刊名称
     */
    private String journalName;
    // 期刊相关简称
    private String isoabbreviation;
    private String pmcabbreviation;
    private String jcrabbreviation;
    private String medlineTa;

    /**
     * 期刊后的内容
     */
    private String journalContent;

    /**
     * 作者列表
     */
    private List<AuthorVO> authors;

    /**
     * 机构列表
     */
    private List<String> affiliation;

    /**
     * 摘要
     */
    private String articleAbstract;

    /**
     * 关键词
     */
    private List<String> keywords;

    /**
     * 发表年份
     */
    private Integer publishedYear;

    /**
     * 发表月份
     */
    private String publishedMonth;

    private Integer publishedDay;


    private Integer year;
    /**
     * 卷号
     */
    private String volume;

    /**
     * 期号
     */
    private String issue;

    /**
     * 页码
     */
    private String page;

    /**
     * 文献分类
     */
    private List<String> pubTypes;

    /**
     * 基金资助
     */
    private List<GrantVO> grants;

    /**
     * 关联数据
     */
    private List<DatabankVO> databanks;

    /**
     * MeSH主题词
     */
    private List<MeshTermVO> meshTerms;
    /**
     * 中科院分区信息
     */
    private Integer zkySections;

    /**
     * 影响因子
     */
    private Double impactFactor;

    /**
     * JCR分区
     */
    private String jcrQuartile;

    /**
     * 引用量（暂无数据）
     */
    private Integer citationCount;

    /**
     * 相似文章列表
     */
    private List<SimilarArticleVO> similarArticles;
}
