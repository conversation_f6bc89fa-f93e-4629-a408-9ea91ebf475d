package org.biosino.lf.plosp.portal.es.entity;

import lombok.Data;
import org.biosino.lf.pds.article.domain.AuthorInfo;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * 文献ES索引实体类
 *
 * <AUTHOR>
 */
@Data
@Document(indexName = PlospArticleEs.ES_INDEX_NAME, createIndex = false)
@Setting(settingPath = "es/es_setting.json")
@Mapping(mappingPath = "es/es_mapping.json")
public class PlospArticleEs {
    public static final String ES_INDEX_NAME = "plosp_article_es";

    /**
     * 文献ID
     */
    @Id
    private Long id;

    private Long pmid;

    @Field(name = "pmc_id")
    private Long pmcId;

    private String doi;

    private List<String> author;

    @Field(name = "author_info")
    private List<AuthorInfo> authorInfo;

    private List<String> affiliation;

    private String title;

    @Field(name = "abstract")
    private String articleAbstract;

    private List<String> keywords;

    @Field(name = "pub_type")
    private List<String> pubType;

    private Long publisherId;

    private Long journalId;

    private String volume;

    private String issue;

    private String page;

    @Field(name = "published_date", type = FieldType.Integer)
    private Integer publishedDate;

    private List<String> grant;

    private List<String> databank;

    private Integer year;

    @Field(name = "journal_name")
    private String journalName;

    @Field(name = "publisher_name")
    private String publisherName;

    private List<String> language;

    @Field(name = "mesh_ui")
    private List<String> meshUi;

    @Field(name = "impact_factor")
    private Float impactFactor;

    private String jcr;

    @Field(name = "large_category_section")
    private Integer largeCategorySection;

    private List<String> source;

    @Field(name = "pub_status")
    private String pubStatus;

    private List<String> mesh;

    private Boolean free;

    private Boolean hasPdf;

    @Field(name = "create_time", type = FieldType.Date)
    private Date createTime;

    @Field(name = "update_time", type = FieldType.Date)
    private Date updateTime;

    @Field(name = "title_vector", type = FieldType.Dense_Vector)
    private float[] titleVector;

    @Field(name = "abstract_vectors", type = FieldType.Nested)
    private List<AbstractVector> abstractVectors;

    @Data
    public static class AbstractVector {
        @Field(name = "vector", type = FieldType.Dense_Vector)
        private float[] vector;

        public AbstractVector() {
        }

        public AbstractVector(float[] vector) {
            this.vector = vector;
        }
    }

}
