package org.biosino.lf.plosp.portal.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.custbean.dto.FileUploadDTO;
import org.biosino.lf.pds.article.custbean.dto.TransmitListQueryDTO;
import org.biosino.lf.pds.article.custbean.dto.api.ArticleTransmitDTO;
import org.biosino.lf.pds.article.custbean.vo.TransmitListVO;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.mapper.ArticleAttachmentUploadMapper;
import org.biosino.lf.pds.article.mapper.ArticleCorrectionMapper;
import org.biosino.lf.pds.article.mapper.ArticleMapper;
import org.biosino.lf.pds.article.mapper.JournalMapper;
import org.biosino.lf.pds.article.service.IArticleService;
import org.biosino.lf.pds.article.service.ITbDdsFileService;
import org.biosino.lf.pds.article.service.ITbUserScoreLogService;
import org.biosino.lf.pds.common.constant.CacheConstants;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.R;
import org.biosino.lf.pds.common.core.mail.MailService;
import org.biosino.lf.pds.common.core.mail.MailTemplateEnum;
import org.biosino.lf.pds.common.core.page.PageDomain;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.pds.common.core.redis.RedisCache;
import org.biosino.lf.pds.common.enums.ArticleAttachmentUploadStatusEnum;
import org.biosino.lf.pds.common.enums.ArticleConrrectionStatusEnum;
import org.biosino.lf.pds.common.enums.task.ArticleAttachmentSourceEnum;
import org.biosino.lf.pds.common.enums.task.FileTypeEnum;
import org.biosino.lf.pds.common.enums.task.ScoreSourceEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.DateUtils;
import org.biosino.lf.pds.common.utils.MyHashUtil;
import org.biosino.lf.pds.common.utils.PDFHelper;
import org.biosino.lf.pds.system.service.ISysConfigService;
import org.biosino.lf.plosp.portal.domain.PortalLoginUser;
import org.biosino.lf.plosp.portal.dto.ArticleCorrectionDTO;
import org.biosino.lf.plosp.portal.dto.ArticleTransmitSubmitDTO;
import org.biosino.lf.plosp.portal.dto.UploadListQueryDTO;
import org.biosino.lf.plosp.portal.utils.PortalSecurityUtils;
import org.biosino.lf.plosp.portal.utils.ThreadPoolUtil;
import org.biosino.lf.plosp.portal.web.service.ArticleTransmitService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文献传递服务层实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleTransmitServiceImpl implements ArticleTransmitService {
    private final RedisCache redisCache;
    private final ITbUserScoreLogService tbUserScoreLogService;
    private final IArticleService articleService;
    private final ITbDdsFileService tbDdsFileService;

    private final ArticleAttachmentUploadMapper articleAttachmentUploadMapper;
    private final ArticleCorrectionMapper articleCorrectionMapper;
    private final JournalMapper journalMapper;
    private final ArticleMapper articleMapper;

    private final MailService mailService;
    private final ISysConfigService sysConfigService;

    @Value("${app.pds-service-url}")
    private String pdsServiceUrl;

    @Value("${app.article-transmit-token}")
    private String articleTransmitToken;
    /**
     * plosp管理后台系统登录URL
     */
    @Value("${app.plosp-admin-url}")
    private String plospAdminUrl;

//    private static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.142 Safari/537.36";

    @Override
    public R<String> articleTransmit(ArticleTransmitSubmitDTO transmitDTO) {
        HttpResponse response = null;
        try {
            // 1. 验证验证码
            final String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + transmitDTO.getUuid();
            final String captcha = redisCache.getCacheObject(verifyKey);

            if (StrUtil.isEmpty(captcha)) {
                return R.fail("验证码已过期，请重新获取");
            }

            if (!captcha.equalsIgnoreCase(transmitDTO.getCaptchaCode())) {
                return R.fail("验证码错误");
            }
            // 验证码使用后删除
            redisCache.deleteObject(verifyKey);

            // 2. 获取当前登录用户信息
            final Long userId = PortalSecurityUtils.getUserId();
            if (userId == null) {
                return R.fail("用户未登录");
            }

            // 3. 构建ArticleTransmitDTO对象
            ArticleTransmitDTO articleTransmitDTO = new ArticleTransmitDTO();
            articleTransmitDTO.setName(transmitDTO.getName());
            articleTransmitDTO.setDescription(transmitDTO.getDescription());
            articleTransmitDTO.setUserId(userId);
            // 文献传递优先级为最大值100
            articleTransmitDTO.setPriority(100);

            // 设置文章ID集合
            final LinkedHashSet<Long> ids = new LinkedHashSet<>();
            ids.add(transmitDTO.getArticleId());
            articleTransmitDTO.setIds(ids);

            articleTransmitDTO.setApiToken(articleTransmitToken);
            // 4. 使用hutool发送POST请求到TaskApiController
            response = HttpRequest.post(pdsServiceUrl + "/pds-api/pub-lds/taskPublish")
                    .timeout(80 * 1000)
                    .body(JSON.toJSONString(articleTransmitDTO))
//                    .header("User-Agent", USER_AGENT, true)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .execute();

            final String bodyStr = response.body();
            if (response.isOk()) {
                final JSONObject jsonObject = JSON.parseObject(bodyStr);
                final String statusStr = jsonObject.getString("status");
                if ("success".equals(statusStr)) {
                    return R.ok("文献传递-获取全文申请提交成功");
                } else {
                    return R.fail("文献传递-获取全文申请提交失败：" + jsonObject.getString("msg"));
                }
            } else {
                return R.fail("文献传递-获取全文申请提交失败：" + bodyStr);
            }

        } catch (Exception e) {
            return R.fail("文献传递-获取全文申请提交失败：" + e.getMessage());
        } finally {
            IoUtil.close(response);
        }
    }

    @Override
    public TableDataInfo transmitList(final TransmitListQueryDTO dto, final long userId, final PageDomain pageDomain) {
        dto.setUserId(userId);
        dto.setPageDomainVal(pageDomain);
        dto.setApiToken(articleTransmitToken);

        HttpResponse response = null;
        try {
            response = HttpRequest.post(pdsServiceUrl + "/pds-api/pub-lds/myTransmitList")
                    .timeout(80 * 1000)
                    .body(JSON.toJSONString(dto))
//                    .header("User-Agent", USER_AGENT, true)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .execute();

            final String bodyStr = response.body();
            if (response.isOk()) {
                final JSONObject jsonObject = JSON.parseObject(bodyStr);
                final String statusStr = jsonObject.getString("status");
                if ("success".equals(statusStr)) {
                    return jsonObject.getObject("data", TableDataInfo.class);
                } else {
                    return null;
                }
            } else {
                throw new ServiceException("文献传递-获取申请列表失败：" + bodyStr);
            }
        } finally {
            IoUtil.close(response);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void downloadArticlePDF(Long docId, Long userId, HttpServletRequest request, HttpServletResponse response) {
        if (userId == null) {
            throw new ServiceException("用户未登录");
        }
        if (docId == null) {
            throw new ServiceException("文献ID不能为空");
        }
        tbUserScoreLogService.downloadArticlePDF(docId, userId, ScoreSourceEnum.download_article_pdf, request, response);
    }

    @Override
    public void downloadMyUploadPDF(Integer uploadId, Long userId, HttpServletRequest request, HttpServletResponse response) {
        if (userId == null) {
            throw new ServiceException("用户未登录");
        }
        if (uploadId == null) {
            throw new ServiceException("上传记录ID不能为空");
        }
        final ArticleAttachmentUpload attachmentUpload = articleAttachmentUploadMapper.selectOne(
                Wrappers.lambdaQuery(ArticleAttachmentUpload.class)
                        .eq(ArticleAttachmentUpload::getCreator, userId)
                        .eq(ArticleAttachmentUpload::getId, uploadId));
        if (attachmentUpload == null) {
            throw new ServiceException("上传记录不存在");
        }
        tbUserScoreLogService.downloadByFileId(attachmentUpload.getAttachmentId(), request, response);
    }

    @Override
    public String upload(final MultipartFile file, final Long docId) {
        // 1. 检查用户是否登录
        final PortalLoginUser loginUser = PortalSecurityUtils.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("请先登录");
        }
        final Long userId = loginUser.getUserId();
        if (userId == null) {
            throw new ServiceException("请先登录");
        }

        if (docId == null) {
            throw new ServiceException("文章id不能为空");
        }

        final Article article = articleService.getById(docId);
        if (article == null) {
            throw new ServiceException("文章不存在:" + docId);
        }

        // 2. 检查文件是否为空
        if (file == null || file.isEmpty()) {
            throw new ServiceException("文件不能为空");
        }

        // 3. 检查文件名是否为PDF格式
        final String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename) || !originalFilename.toLowerCase().endsWith(".pdf")) {
            throw new ServiceException("只能上传PDF格式的文件");
        }

        File tempFile = null;
        try {
            // 4. 创建临时文件进行PDF检验
            tempFile = File.createTempFile("upload_", ".pdf");
            file.transferTo(tempFile);

            // 5. 使用PDFHelper检查文件是否为合法的PDF
            if (!PDFHelper.checkPDF(tempFile)) {
                throw new ServiceException("上传的文件不是有效的PDF文件");
            }

            // 6. 计算文件MD5
            final String fileMd5 = MyHashUtil.md5(tempFile);
            final ArticleAttachmentUpload existUpload = articleAttachmentUploadMapper.findOne(Wrappers.lambdaQuery(ArticleAttachmentUpload.class)
                    .eq(ArticleAttachmentUpload::getDocId, docId)
                    .eq(ArticleAttachmentUpload::getMd5, fileMd5)
                    .select(ArticleAttachmentUpload::getId));

            if (existUpload != null) {
                throw new ServiceException("该文件已上传过，请勿重复上传文件");
            }
            // 7. 使用ITbDdsFileService保存文件
            FileUploadDTO uploadDTO = new FileUploadDTO(
                    tempFile,
                    FileTypeEnum.PDF,
                    fileMd5,
                    originalFilename,
                    ArticleAttachmentSourceEnum.success_plosp.name(),
                    null,
                    true  // checkExist
            );

            final TbDdsFile uploadedFile = tbDdsFileService.upload(uploadDTO);

            // 8. 保存上传记录到tb_dds_article_attachment_upload表
            final ArticleAttachmentUpload attachmentUpload = new ArticleAttachmentUpload();
            attachmentUpload.setDocId(docId);
            // 获取文章标题
            attachmentUpload.setTitle(article.getTitle());
            attachmentUpload.setAttachmentId(uploadedFile.getId());
            attachmentUpload.setCreator(userId);
            attachmentUpload.setCreateTime(new Date());
            attachmentUpload.setStatus(ArticleAttachmentUploadStatusEnum.VERIFY_WAITING.getCode());  // 0-待审核
            attachmentUpload.setMd5(fileMd5);
            attachmentUpload.setFileName(uploadedFile.getFileName());

            articleAttachmentUploadMapper.insert(attachmentUpload);

            // 完成邮件通知
            final String emailStr = sysConfigService.selectConfigByKey("plosp.admin.email");
            final Map<String, Object> emailInfo = new HashMap<>();
            emailInfo.put("username", loginUser.getUsername());
            emailInfo.put("articleTitle", article.getTitle());
            emailInfo.put("createTime", attachmentUpload.getCreateTime());
            emailInfo.put("plospAdminUrl", plospAdminUrl);
            sendEmailToAdmin(emailStr, emailInfo, MailTemplateEnum.PLOSP_PORTAL_UPLOAD_PDF);

            return "文件上传成功，等待审核";
        } catch (Exception e) {
            log.info("文件上传失败", e);
            if (e instanceof ServiceException) {
                throw (ServiceException) e;
            }
            throw new ServiceException("文件上传失败：" + e.getMessage());
        } finally {
            //删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    public void sendEmailToAdmin(final String emailStr, final Map<String, Object> params, MailTemplateEnum templateEnum) {
        if (StrUtil.isNotBlank(emailStr) && CollUtil.isNotEmpty(params)) {
            ThreadPoolUtil.getExecutor().execute(() -> {
                final String[] emails = emailStr.split(",");
                mailService.sendMailsWithTemplate(emails, templateEnum, params);
            });
        }
    }


    @Override
    public TableDataInfo myUpload(final UploadListQueryDTO queryDTO, final Long userId, final PageDomain pageDomain) {
        if (userId == null) {
            throw new ServiceException("用户未登录");
        }
        final LambdaQueryWrapper<ArticleAttachmentUpload> queryWrapper = Wrappers.lambdaQuery(ArticleAttachmentUpload.class);
        final Integer status = queryDTO.getStatus();
        if (status != null) {
            queryWrapper.eq(ArticleAttachmentUpload::getStatus, status);
        }

        final String beginTime = queryDTO.getBeginTime();
        if (StrUtil.isNotBlank(beginTime)) {
            queryWrapper.ge(ArticleAttachmentUpload::getCreateTime, DateUtil.beginOfDay(DateUtils.parseDate(beginTime)));
        }

        final String endTime = queryDTO.getEndTime();
        if (StrUtil.isNotBlank(endTime)) {
            queryWrapper.le(ArticleAttachmentUpload::getCreateTime, DateUtil.endOfDay(DateUtils.parseDate(endTime)));
        }


        final String articleTitle = queryDTO.getArticleTitle();
        if (StrUtil.isNotEmpty(articleTitle)) {
            final List<ArticleAttachmentUpload> uploadList = articleAttachmentUploadMapper.selectList(queryWrapper.clone()
                    .isNotNull(ArticleAttachmentUpload::getDocId).select(ArticleAttachmentUpload::getDocId));
            Set<Long> allDocIds = null;
            if (CollUtil.isNotEmpty(uploadList)) {
                allDocIds = uploadList.stream().map(ArticleAttachmentUpload::getDocId).collect(Collectors.toSet());
            }

            final Set<Long> docIdSet = TransmitListVO.findArticleSearchIds(allDocIds, articleTitle, articleMapper);
            if (CollUtil.isNotEmpty(docIdSet)) {
                queryWrapper.in(ArticleAttachmentUpload::getDocId, docIdSet);
            } else {
                queryWrapper.eq(ArticleAttachmentUpload::getId, -1);
            }
        }

        queryWrapper.orderByDesc(ArticleAttachmentUpload::getCreateTime);

        final PageDTO<ArticleAttachmentUpload> page = new PageDTO<>(pageDomain.getPageNum(), pageDomain.getPageSize());
        final PageDTO<ArticleAttachmentUpload> result = articleAttachmentUploadMapper.selectPage(page, queryWrapper);


        final List<ArticleAttachmentUpload> records = result.getRecords();

        final List<TransmitListVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            final Set<Long> docIds = records.stream().map(ArticleAttachmentUpload::getDocId).filter(Objects::nonNull).collect(Collectors.toSet());
            final List<Article> articles = articleService.list(Wrappers.lambdaQuery(Article.class).in(Article::getId, docIds));

            final Map<Long, Article> docIdArticleMap = new HashMap<>();
            final Set<Long> journalIds = new HashSet<>();
            for (Article article : articles) {
                Long id = article.getId();
                docIdArticleMap.put(id, article);

                Long journalId = article.getJournalId();
                if (journalId != null) {
                    journalIds.add(journalId);
                }
            }
            final Map<Long, String> journalTitleMap = journalMap(journalIds);

//            final Map<Integer, String> statusMap = ArticleAttachmentUploadStatusEnum.toMap();
            for (ArticleAttachmentUpload item : records) {
                final TransmitListVO vo = TransmitListVO.init(item.getCreateTime(), item.getDocId(), docIdArticleMap, journalTitleMap);
                vo.setStatus(item.getStatus().toString());
                vo.setReason(item.getReason());
                vo.setFileName(item.getFileName());
                vo.setUploadId(item.getId());
                list.add(vo);
            }
        }

        // return new ApiResultVO("success", "查询我的上传列表成功", tableDataInfo);
        return BaseController.initTableDataInfo(list, result.getTotal());
    }

    private Map<Long, String> journalMap(final Set<Long> journalIds) {
        final Map<Long, String> map = new HashMap<>();
        if (CollUtil.isEmpty(journalIds)) {
            return map;
        }
        final List<Journal> journals = journalMapper.selectList(Wrappers.lambdaQuery(Journal.class).in(Journal::getId, journalIds));
        for (Journal journal : journals) {
            map.put(journal.getId(), journal.getTitle());
        }
        return map;
    }


    @Override
    public R<String> submitCorrection(ArticleCorrectionDTO correctionDTO) {
        final Long userId = PortalSecurityUtils.getUserId();
        if (userId == null) {
            throw new ServiceException("用户未登录");
        }

        // 2. 构建ArticleCorrection对象
        final ArticleCorrection articleCorrection = new ArticleCorrection();
        articleCorrection.setDocId(correctionDTO.getDocId());
        articleCorrection.setTitle(correctionDTO.getTitle());
        articleCorrection.setCorrectionType(correctionDTO.getCorrectionType());
        articleCorrection.setContent(correctionDTO.getContent());
        articleCorrection.setCreator(userId);
        articleCorrection.setCreateTime(new Date());
        articleCorrection.setStatus(ArticleConrrectionStatusEnum.VERIFY_WAITING.getCode()); // 初始状态为未处理

        // 3. 保存到数据库
        int result = articleCorrectionMapper.insert(articleCorrection);
        if (result > 0) {
            return R.ok("纠错信息提交成功，等待审核中");
        } else {
            return R.fail("纠错信息提交失败");
        }
    }

    @Override
    public TableDataInfo myCorrectionList(final UploadListQueryDTO queryDTO, final Long userId, final PageDomain pageDomain) {
        if (userId == null) {
            throw new ServiceException("用户未登录");
        }
        final LambdaQueryWrapper<ArticleCorrection> queryWrapper = Wrappers.lambdaQuery(ArticleCorrection.class);
        final Integer status = queryDTO.getStatus();
        if (status != null) {
            queryWrapper.eq(ArticleCorrection::getStatus, status);
        }

        final String beginTime = queryDTO.getBeginTime();
        if (StrUtil.isNotBlank(beginTime)) {
            queryWrapper.ge(ArticleCorrection::getCreateTime, DateUtil.beginOfDay(DateUtils.parseDate(beginTime)));
        }

        final String endTime = queryDTO.getEndTime();
        if (StrUtil.isNotBlank(endTime)) {
            queryWrapper.le(ArticleCorrection::getCreateTime, DateUtil.endOfDay(DateUtils.parseDate(endTime)));
        }

        final String articleTitle = queryDTO.getArticleTitle();
        if (StrUtil.isNotEmpty(articleTitle)) {
            final List<ArticleCorrection> dbList = articleCorrectionMapper.selectList(queryWrapper.clone()
                    .isNotNull(ArticleCorrection::getDocId).select(ArticleCorrection::getDocId));
            Set<Long> allDocIds = null;
            if (CollUtil.isNotEmpty(dbList)) {
                allDocIds = dbList.stream().map(ArticleCorrection::getDocId).collect(Collectors.toSet());
            }

            final Set<Long> docIdSet = TransmitListVO.findArticleSearchIds(allDocIds, articleTitle, articleMapper);
            if (CollUtil.isNotEmpty(docIdSet)) {
                queryWrapper.in(ArticleCorrection::getDocId, docIdSet);
            } else {
                queryWrapper.eq(ArticleCorrection::getId, -1);
            }
        }

        queryWrapper.orderByDesc(ArticleCorrection::getCreateTime);

        final PageDTO<ArticleCorrection> page = new PageDTO<>(pageDomain.getPageNum(), pageDomain.getPageSize());
        final PageDTO<ArticleCorrection> result = articleCorrectionMapper.selectPage(page, queryWrapper);


        final List<ArticleCorrection> records = result.getRecords();

        final List<TransmitListVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            final Set<Long> docIds = records.stream().map(ArticleCorrection::getDocId).filter(Objects::nonNull).collect(Collectors.toSet());
            final List<Article> articles = articleService.list(Wrappers.lambdaQuery(Article.class).in(Article::getId, docIds));

            final Map<Long, Article> docIdArticleMap = new HashMap<>();
            final Set<Long> journalIds = new HashSet<>();
            for (Article article : articles) {
                Long id = article.getId();
                docIdArticleMap.put(id, article);

                Long journalId = article.getJournalId();
                if (journalId != null) {
                    journalIds.add(journalId);
                }
            }
            final Map<Long, String> journalTitleMap = journalMap(journalIds);

//            final Map<Integer, String> statusMap = ArticleAttachmentUploadStatusEnum.toMap();
            for (ArticleCorrection item : records) {
                final TransmitListVO vo = TransmitListVO.init(item.getCreateTime(), item.getDocId(), docIdArticleMap, journalTitleMap);
                vo.setStatus(item.getStatus().toString());
                vo.setCorrectionType(item.getCorrectionType());
                vo.setCorrectionContent(item.getContent());
                vo.setReason(item.getReason());
                list.add(vo);
            }
        }

        // return new ApiResultVO("success", "查询我的上传列表成功", tableDataInfo);
        return BaseController.initTableDataInfo(list, result.getTotal());
    }
}
