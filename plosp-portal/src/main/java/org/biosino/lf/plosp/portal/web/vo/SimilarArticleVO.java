package org.biosino.lf.plosp.portal.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 相似文章VO
 *
 * <AUTHOR>
 */
@Data
public class SimilarArticleVO {

    /**
     * 文章ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * PMID
     */
    private Long pmid;

    /**
     * PMCID
     */
    private Long pmcId;

    /**
     * DOI
     */
    private String doi;

    /**
     * 期刊名称
     */
    private String journalName;

    /**
     * 发表年份
     */
    private Integer year;

    /**
     * 相似度分数
     */
//    private Float similarityScore;

    /**
     * 摘要（截取前200字符）
     */
    private String abstractPreview;
}