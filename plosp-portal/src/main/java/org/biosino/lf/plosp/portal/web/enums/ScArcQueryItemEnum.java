package org.biosino.lf.plosp.portal.web.enums;

import lombok.Getter;
import org.biosino.lf.plosp.portal.web.vo.SelectVO;


import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */

@Getter
public enum ScArcQueryItemEnum {
    ai("AI智能搜索"),
    pmid("PMID"),
    pmc_id("PMCID"),
    doi("DOI"),
    title("标题"),
    author("作者"),
    lastname("姓"),
    forename("名"),
    affiliation("作者单位"),
    articleAbstract("摘要"),
    keywords("关键词"),
    pub_type("文献分类"),
    publisherName("出版社名称"),
    journalName("期刊名称"),
    volume("卷"),
    issue("期"),
    page("页码"),
    published_date_range("发表日期"),
    language("语言"),
    grant("基金与资助"),
    databank("相关数据"),
    source("文献来源"),
    pub_status("发表状态"),
    meshUi("MeSH");

    private final String text;

    ScArcQueryItemEnum(String text) {
        this.text = text;
    }

    public static List<SelectVO> getSelectVOList() {
        final ScArcQueryItemEnum[] values = values();
        final List<SelectVO> list = new ArrayList<>();
        for (ScArcQueryItemEnum value : values) {
            final SelectVO vo = new SelectVO();
            vo.setLabel(value.getText());
            vo.setValue(value.name());
            vo.setSelected(value == ai);
            list.add(vo);
        }
        return list;
    }

    public static Optional<ScArcQueryItemEnum> findByName(String name) {
        if (name == null) {
            return Optional.empty();
        }
        final ScArcQueryItemEnum[] values = values();
        for (ScArcQueryItemEnum value : values) {
            if (value.name().equals(name)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }
}
