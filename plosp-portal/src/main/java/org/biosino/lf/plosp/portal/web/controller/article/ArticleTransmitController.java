package org.biosino.lf.plosp.portal.web.controller.article;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.custbean.dto.TransmitListQueryDTO;
import org.biosino.lf.pds.common.core.controller.GlobalBaseController;
import org.biosino.lf.pds.common.core.domain.R;
import org.biosino.lf.pds.common.core.page.PageDomain;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.pds.common.core.page.TableSupport;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.plosp.portal.dto.ArticleCorrectionDTO;
import org.biosino.lf.plosp.portal.dto.ArticleTransmitSubmitDTO;
import org.biosino.lf.plosp.portal.dto.UploadListQueryDTO;
import org.biosino.lf.plosp.portal.utils.PortalSecurityUtils;
import org.biosino.lf.plosp.portal.web.service.ArticleTransmitService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文献传递控制层
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/transmit")
public class ArticleTransmitController extends GlobalBaseController {
    private final ArticleTransmitService articleTransmitService;

    /**
     * 文献传递
     */
    @PostMapping("/articleTransmit")
    public R<String> articleTransmit(@Validated @RequestBody final ArticleTransmitSubmitDTO transmitDTO) {
        return articleTransmitService.articleTransmit(transmitDTO);
    }

    /**
     * 我的文献传递列表
     */
    @GetMapping("/transmitList")
    public TableDataInfo transmitList(final TransmitListQueryDTO dto) {
        final Long userId = PortalSecurityUtils.getUserId();
        if (userId == null) {
            throw new ServiceException("用户未登录");
        }

        final PageDomain pageDomain = TableSupport.buildPageRequest();
        return articleTransmitService.transmitList(dto, userId, pageDomain);
    }

    /**
     * 文献传递--下载文章PDF
     */
    @PostMapping("/downloadArticlePDF")
    public void downloadArticlePDF(final Long docId, final HttpServletRequest request, final HttpServletResponse response) {
        final Long userId = PortalSecurityUtils.getUserId();
        articleTransmitService.downloadArticlePDF(docId, userId, request, response);
    }

    /**
     * 我的上传--下载文章PDF
     */
    @PostMapping("/downloadMyUploadPDF")
    public void downloadMyUploadPDF(final Integer uploadId, final HttpServletRequest request, final HttpServletResponse response) {
        final Long userId = PortalSecurityUtils.getUserId();
        articleTransmitService.downloadMyUploadPDF(uploadId, userId, request, response);
    }

    /**
     * 上传文献PDF文件
     */
    @PostMapping("/upload")
    public R<String> uploadPdf(
            @RequestParam("file") MultipartFile file,
            @RequestParam("docId") Long docId) {
        String result = articleTransmitService.upload(file, docId);
        return R.ok(result);
    }

    /**
     * 我的上传列表
     */
    @GetMapping("/myUpload")
    public TableDataInfo myUpload(final UploadListQueryDTO queryDTO) {
        final Long userId = PortalSecurityUtils.getUserId();
        final PageDomain pageDomain = TableSupport.buildPageRequest();
        return articleTransmitService.myUpload(queryDTO, userId, pageDomain);
    }

    /**
     * 提交文献纠错信息
     *
     * @param correctionDTO 纠错信息DTO
     * @return 操作结果
     */
    @PostMapping("/correctionSubmit")
    public R<String> submitCorrection(@Validated @RequestBody final ArticleCorrectionDTO correctionDTO) {
        return articleTransmitService.submitCorrection(correctionDTO);
    }

    /**
     * 我的纠错信息列表
     */
    @GetMapping("/myCorrectionList")
    public TableDataInfo myCorrectionList(final UploadListQueryDTO queryDTO) {
        final Long userId = PortalSecurityUtils.getUserId();
        final PageDomain pageDomain = TableSupport.buildPageRequest();
        return articleTransmitService.myCorrectionList(queryDTO, userId, pageDomain);
    }
}