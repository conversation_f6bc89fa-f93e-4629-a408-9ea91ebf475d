<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.plosp.portal.web.mapper.UserHistoryMapper">

    <resultMap id="UserHistoryResult" type="org.biosino.lf.plosp.portal.web.vo.UserDocHistoryVO">
        <id property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="title" column="title"/>
        <result property="author" column="author"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="userId" column="user_id"/>
        <result property="journalId" column="journal_id"/>
        <result property="year" column="year"/>
        <result property="volume" column="volume"/>
        <result property="issue" column="issue"/>
        <result property="page" column="page"/>
        <result property="doi" column="doi"/>
        <result property="pmid" column="pmid"/>
        <result property="pmcId" column="pmc_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="listByUserId" parameterType="org.biosino.lf.plosp.portal.web.dto.UserDocHistoryDTO"
            resultMap="UserHistoryResult">
        SELECT
        h.id,
        h.user_id,
        h.doc_id,
        d.pmid,
        d.pmc_id,
        d.doi,
        d.title,
        d.author,
        d.year,
        d.volume,
        d.issue,
        d.page,
        d.journal_id,
        h.create_time
        FROM tb_user_doc_history h
        INNER JOIN tb_dds_article d ON h.doc_id = d.id
        <where>
            <if test="userId != null">
                AND h.user_id = #{userId}
            </if>
            <if test="pmid != null">
                AND d.pmid = #{pmid}
            </if>
            <if test="pmcId != null and pmcId != ''">
                AND d.pmc_id = #{pmcId}
            </if>
            <if test="doi != null and doi != ''">
                AND d.doi = #{doi}
            </if>
            <if test="title != null and title != ''">
                AND d.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="startTime != null">
                AND h.create_time >= (#{startTime})
            </if>
            <if test="endTime != null">
                AND h.create_time &lt;= (#{endTime})
            </if>
        </where>
        ORDER BY h.create_time DESC
    </select>

</mapper>
