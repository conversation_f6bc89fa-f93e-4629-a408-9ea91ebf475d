# 测试环境配置
server:
  # 服务器的HTTP端口
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /keep-wh/plosp-portal-admin

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: org.postgresql.Driver
    # 测试环境数据库连接 (请根据实际环境修改)
    url: ******************************************************************************************************************************************************************************************
    username: postgres
    password: Biosino+2025
    druid:
      # 测试环境连接池配置
      initialSize: 10
      minIdle: 20
      maxActive: 40
      maxWait: 60000
      connectTimeout: 600000
      socketTimeout: 600000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT version()
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username: test
        login-password: test123
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  data:
    redis:
      # 地址
      host: dev-redis
      # 端口，默认为6379
      port: 6379
      # 数据库索引 (使用不同的数据库索引以避免与admin冲突)
      database: 2
      # 密码
      password:
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
  # 邮件配置 (测试环境)
  mail:
    # 默认编码
    default-encoding: UTF-8
    host: mail.cstnet.cn
    username: <EMAIL>
    password: nXAaY*7H&^Cgu3%q
    port: 994
    properties.mail.smtp:
      ssl:
        enable: true
      socketFactory:
        class: javax.net.ssl.SSLSocketFactory
  # Elasticsearch配置 (使用Spring Data Elasticsearch)
  elasticsearch:
    uris: http://dev-es8-svc:9200
    connection-timeout: 30s
    socket-timeout: 300s  # 增加到5分钟，支持大数据量删除操作
  # Spring AI配置
  ai:
    embedding:
      transformer:
        enabled: false
        onnx:
          model-uri: file:/data/plosp-portal/model/all-MiniLM-L6-v2/model.onnx
          model-output-name: token_embeddings
        tokenizer:
          uri: file:/data/plosp-portal/model/all-MiniLM-L6-v2/tokenizer.json
          options:
            maxLength: 512
  # 定时任务线程池配置
  task:
    scheduling:
      pool:
        size: 2                      # 定时任务线程池大小
      thread-name-prefix: "es-scheduled-"

# 向量化服务配置
embedding:
  service:
    url: https://dev.biosino.org/zj-knowledge-api/text_embedding
    api-key: sk-8DjtyDqlvKwy1915krV74Oac5-mcWvEt
    timeout: 15000          # 请求超时时间（毫秒）- 减少到15秒
    retry:
      maxAttempts: 2        # 最大重试次数 - 减少到2次
      baseDelay: 500        # 基础延迟时间（毫秒）- 减少到500ms
      maxDelay: 3000        # 最大延迟时间（毫秒）- 减少到3秒

es:
  batch:
    size: 1000  # 每批处理的数据量
  # 线程池配置
  thread:
    pool:
      core: 2      # 核心线程数
      max: 15       # 最大线程数
      queue: 100   # 队列容量
  # 向量配置
  vector:
    enabled: true   # 是否启用向量生成

  # 启动配置
  startup:
    fullRefresh: true  # 启动时是否执行全量刷新
  # 定时任务配置
  scheduled:
    enabled: true                    # 是否启用定时任务
    cron: "0 0 2 * * ?"             # 每天凌晨2点执行
    incremental-hours: 24

# 测试环境日志配置
logging:
  level:
    org.biosino.lf.plosp.portal: info
    org.biosino.lf.pds: info
    org.springframework.security: info
    org.springframework.web: info
    org.springframework: info
    org.springframework.ai.transformer: warn
    org.biosino.lf.plosp.portal.config: warn


# 应用配置
app:
  # 测试环境文件路径
  profile: /data/plosp-portal/uploadPath
  data-home: /data/plosp-portal/data
  # estoken
  es-token: 3c8661430cdd56d329af2914b93bdc07tzrG6kLxvMOV
  pds-service-url: https://dev.biosino.org/keep-wh/pds-admin
  article-transmit-token: plospApiToken-b8a3188b600383A1a079Cc45F5899afbA7tkXuvU
  plosp-admin-url: https://dev.biosino.org/keep-wh/plosp-admin


# 测试环境token配置
token:
  # 测试环境令牌有效期（单位分钟）
  expireTime: 1440

deep-seek:
  api: sk-d34831d3919e4689b1a8c6fffc341f22

