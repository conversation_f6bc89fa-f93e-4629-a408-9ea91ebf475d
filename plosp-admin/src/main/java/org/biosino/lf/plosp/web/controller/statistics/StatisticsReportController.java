package org.biosino.lf.plosp.web.controller.statistics;

import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.statistics.*;
import org.biosino.lf.pds.article.mapper.statistics.*;
import org.biosino.lf.pds.system.domain.statistics.StatisticsUser;
import org.biosino.lf.pds.system.mapper.statistics.StatisticsUserMapper;
import org.biosino.lf.pds.common.utils.poi.ExcelUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/3
 */
@RestController
@RequestMapping("/statistics/report")
@RequiredArgsConstructor
public class StatisticsReportController {

    private final StatisticsArticleMapper statisticsArticleMapper;
    private final StatisticsArticlePublishedMapper statisticsArticlePublishedMapper;
    private final StatisticsArticleDataVolumeMapper statisticsArticleDataVolumeMapper;
    private final StatisticsArticleDownloadMapper statisticsArticleDownloadMapper;
    private final StatisticsJournalMapper statisticsJournalMapper;
    private final StatisticsJournalPublishedMapper statisticsJournalPublishedMapper;
    private final StatisticsUserMapper statisticsUserMapper;

    @PostMapping("/export/article")
    public void exportArticle(HttpServletResponse response) {
        List<StatisticsArticle> list = statisticsArticleMapper.selectList(null);
        ExcelUtil<StatisticsArticle> util = new ExcelUtil<>(StatisticsArticle.class);
        util.exportExcel(response, list, "系统文献统计");
    }

    @PostMapping("/export/articlePublish")
    public void exportArticlePublished(HttpServletResponse response) {
        List<StatisticsArticlePublished> list = statisticsArticlePublishedMapper.selectList(null);
        ExcelUtil<StatisticsArticlePublished> util = new ExcelUtil<>(StatisticsArticlePublished.class);
        util.exportExcel(response, list, "发布文献统计");
    }

    @PostMapping("/export/articleDataVolume")
    public void exportArticleDataVolume(HttpServletResponse response) {
        List<StatisticsArticleDataVolume> list = statisticsArticleDataVolumeMapper.selectList(null);
        ExcelUtil<StatisticsArticleDataVolume> util = new ExcelUtil<>(StatisticsArticleDataVolume.class);
        util.exportExcel(response, list, "文献数据量统计");
    }

    @PostMapping("/export/articleDownload")
    public void exportArticleDownload(HttpServletResponse response) {
        List<StatisticsArticleDownload> list = statisticsArticleDownloadMapper.selectList(null);
        ExcelUtil<StatisticsArticleDownload> util = new ExcelUtil<>(StatisticsArticleDownload.class);
        util.exportExcel(response, list, "文献下载量统计");
    }

    @PostMapping("/export/journal")
    public void exportJournal(HttpServletResponse response) {
        List<StatisticsJournal> list = statisticsJournalMapper.selectList(null);
        ExcelUtil<StatisticsJournal> util = new ExcelUtil<>(StatisticsJournal.class);
        util.exportExcel(response, list, "系统期刊统计");
    }

    @PostMapping("/export/journalPublished")
    public void exportJournalPublished(HttpServletResponse response) {
        List<StatisticsJournalPublished> list = statisticsJournalPublishedMapper.selectList(null);
        ExcelUtil<StatisticsJournalPublished> util = new ExcelUtil<>(StatisticsJournalPublished.class);
        util.exportExcel(response, list, "发布期刊统计");
    }

    @PostMapping("/export/user")
    public void exportUser(HttpServletResponse response) {
        List<StatisticsUser> list = statisticsUserMapper.selectList(null);
        ExcelUtil<StatisticsUser> util = new ExcelUtil<>(StatisticsUser.class);
        util.exportExcel(response, list, "用户数统计");
    }
}
