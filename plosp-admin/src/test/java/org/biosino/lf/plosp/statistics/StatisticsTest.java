package org.biosino.lf.plosp.statistics;

import org.biosino.lf.plosp.task.service.statistics.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2025/8/29
 */
@SpringBootTest
public class StatisticsTest {

    @Autowired
    private StatisticsArticleService articleService;

    @Autowired
    private StatisticsArticlePublishedService articlePublishedService;

    @Autowired
    private StatisticsArticleDownloadService articleDownloadService;

    @Autowired
    private StatisticsJournalService statisticsJournalService;

    @Autowired
    private StatisticsJournalPublishedService statisticsJournalPublishedService;

    @Autowired
    private StatisticsArticleDataVolumeService statisticsArticleDataVolumeService;

    @Test
    public void test1() {
        articleService.calculateAll();
    }

    @Test
    public void test2() {
        articlePublishedService.calculateAll();
    }

    @Test
    public void test3() {
        articleDownloadService.calculateByMonth();
    }

    @Test
    public void test4() {
        statisticsJournalService.calculateAll();
    }

    @Test
    public void test5() {
        statisticsJournalPublishedService.calculateAll();
    }

    @Test
    public void test6() {
        statisticsArticleDataVolumeService.calculateAll();
    }
}
