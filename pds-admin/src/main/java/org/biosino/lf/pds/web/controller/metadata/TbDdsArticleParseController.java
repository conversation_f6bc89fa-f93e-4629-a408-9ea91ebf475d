package org.biosino.lf.pds.web.controller.metadata;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.ArticleParse;
import org.biosino.lf.pds.article.dto.ArticleParseDTO;
import org.biosino.lf.pds.common.annotation.Log;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.pds.common.enums.BusinessType;
import org.biosino.lf.pds.task.service.ITbDdsArticleParseService;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文献解析管理
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/parse")
public class TbDdsArticleParseController extends BaseController {
    private final ITbDdsArticleParseService tbDdsArticleParseService;

    /**
     * 查询文献解析列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ArticleParseDTO articleParseDTO) {
        startPage();
        List<ArticleParse> list = tbDdsArticleParseService.selectParseList(articleParseDTO);
        return getDataTable(list);
    }

    /**
     * 下载文档
     */
    @PostMapping("/download/{id}")
    public ResponseEntity<Resource> download(@PathVariable Long id) {
        Resource resource = tbDdsArticleParseService.downloadFile(id);
        // 获取文件名
        ArticleParse articleParse = tbDdsArticleParseService.getById(id);
        String fileName = articleParse.getFileName();

        return ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                .header("Content-Type", "application/octet-stream")
                .body(resource);
    }

    /**
     * 删除文献解析和文献
     */
    @DeleteMapping("/delete/{id}")
    @PreAuthorize("@ss.hasPermi('parse:delete')")
    @Log(title = "解析管理", businessType = BusinessType.DELETE)
    public AjaxResult deleteParse(@PathVariable Long[] id) {
        tbDdsArticleParseService.deleteParses(id);
        return AjaxResult.success("删除成功");
    }

    /**
     * 更新文献解析
     */
    @PostMapping("/retry/{id}")
    @PreAuthorize("@ss.hasPermi('parse:retry')")
    @Log(title = "解析管理", businessType = BusinessType.UPDATE)
    public AjaxResult saveParse(@PathVariable Long[] id) {
        tbDdsArticleParseService.retryParse(id);
        return AjaxResult.success("修改成功");
    }
}
