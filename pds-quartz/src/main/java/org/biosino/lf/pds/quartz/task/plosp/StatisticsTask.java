package org.biosino.lf.pds.quartz.task.plosp;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.plosp.task.service.statistics.*;
import org.springframework.stereotype.Component;

/**
 * 统计定时任务调度
 *
 * <AUTHOR>
 */
@Slf4j
@Component("statisticsTask")
@RequiredArgsConstructor
public class StatisticsTask {
    private final StatisticsArticleService statisticsArticleService;
    private final StatisticsArticleDataVolumeService statisticsArticleDataVolumeService;
    private final StatisticsArticlePublishedService statisticsArticlePublishedService;
    private final StatisticsArticleDownloadService statisticsArticleDownloadService;
    private final StatisticsJournalService statisticsJournalService;
    private final StatisticsJournalPublishedService statisticsJournalPublishedService;
    private final StatisticsUserService statisticsUserService;

    /**
     * 统计文章
     */
    public void article() {
        statisticsArticleService.calculateByMonth();
    }

    /**
     * 统计文章数据量
     */
    public void articleDataVolume() {
        statisticsArticleDataVolumeService.calculateByMonth();
    }

    /**
     * 统计文章发表
     */
    public void articlePublished() {
        statisticsArticlePublishedService.calculateByMonth();
    }

    /**
     * 统计文章下载
     */
    public void articleDownload() {
        statisticsArticleDownloadService.calculateByMonth();
    }

    /**
     * 统计期刊
     */
    public void journal() {
        statisticsJournalService.calculateByMonth();
    }

    /**
     * 统计期刊发表
     */
    public void journalPublished() {
        statisticsJournalPublishedService.calculateByMonth();
    }

    /**
     * 统计用户
     */
    public void user() {
        statisticsUserService.calculateByMonth();
    }


    /**
     * 全量初始化
     */
    public void initStat() {
        log.info("开始文章统计全量计算...");
        statisticsArticleService.calculateAll();
        log.info("文章统计全量计算完成");

        log.info("开始文章数据量统计全量计算...");
        statisticsArticleDataVolumeService.calculateAll();
        log.info("文章数据量统计全量计算完成");

        log.info("开始文章发表统计全量计算...");
        statisticsArticlePublishedService.calculateAll();
        log.info("文章发表统计全量计算完成");

        log.info("开始文章下载统计全量计算...");
        statisticsArticleDownloadService.calculateByMonth();
        log.info("文章下载统计全量计算完成");

        log.info("开始期刊统计全量计算...");
        statisticsJournalService.calculateAll();
        log.info("期刊统计全量计算完成");

        log.info("开始期刊发表统计全量计算...");
        statisticsJournalPublishedService.calculateAll();
        log.info("期刊发表统计全量计算完成");

        log.info("开始用户统计全量计算...");
        statisticsUserService.calculateAll();
        log.info("用户统计全量计算完成");
    }
}
