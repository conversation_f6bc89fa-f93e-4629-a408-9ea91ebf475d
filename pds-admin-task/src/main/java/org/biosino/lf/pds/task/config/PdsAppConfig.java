package org.biosino.lf.pds.task.config;

import lombok.RequiredArgsConstructor;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * PDS yml配置数据
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PdsAppConfig {
    private final Environment environment;

    /**
     * 分配期刊脚本时，一次添加的脚本的最大期刊数量
     */
    private Integer maxAddScriptNum;

    /**
     * 默认API令牌
     */
    private String defaultApiToken;

    /**
     * 判断握手超时时间间隔（单位分钟）
     */
    private Integer handshakeInterval;

    /**
     * 完整度优先时，每个节点类型的最大重试数量
     */
    private Integer paperMaxRetryTimes;

    public Integer getMaxAddScriptNum() {
        if (maxAddScriptNum == null) {
            maxAddScriptNum = environment.getProperty("pds.maxAddScriptNum", Integer.class, 20000);
        }
        return maxAddScriptNum;
    }

    public String getDefaultApiToken() {
        if (defaultApiToken == null) {
            defaultApiToken = environment.getProperty("pds.defaultApiToken", "989e8cD4a70A35f295f25f58d8236c63Sep5wTSE");
        }
        return defaultApiToken;
    }

    public Integer getHandshakeInterval() {
        if (handshakeInterval == null) {
            handshakeInterval = environment.getProperty("pds.handshakeInterval", Integer.class, 3);
        }
        return handshakeInterval;
    }

    public Integer getPaperMaxRetryTimes() {
        if (paperMaxRetryTimes == null) {
            paperMaxRetryTimes = environment.getProperty("pds.paperMaxRetryTimes", Integer.class, 3);
        }
        return paperMaxRetryTimes;
    }
}
