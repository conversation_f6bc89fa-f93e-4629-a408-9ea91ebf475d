package org.biosino.lf.pds.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.biosino.lf.pds.article.custbean.dto.NodeMonitorExportDTO;
import org.biosino.lf.pds.article.custbean.dto.SiteDTO;
import org.biosino.lf.pds.article.custbean.dto.SiteStatusDTO;
import org.biosino.lf.pds.article.custbean.dto.SystemInfoDTO;
import org.biosino.lf.pds.article.custbean.vo.SiteLogVO;
import org.biosino.lf.pds.article.custbean.vo.SiteVO;
import org.biosino.lf.pds.article.custbean.vo.StatInfoVO;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.mapper.*;
import org.biosino.lf.pds.article.service.ITbDdsFileService;
import org.biosino.lf.pds.common.constant.CacheConstants;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.redis.RedisCache;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.enums.task.ScriptTypeEnum;
import org.biosino.lf.pds.common.enums.task.SiteRunStatusEnum;
import org.biosino.lf.pds.common.enums.task.TaskPaperScheduleStatusEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.poi.ExcelUtil;
import org.biosino.lf.pds.task.config.PdsAppConfig;
import org.biosino.lf.pds.task.service.ITbDdsSiteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 节点管理服务层实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbDdsSiteServiceImpl extends ServiceImpl<TbDdsSiteMapper, TbDdsSite> implements ITbDdsSiteService {
    private final RedisCache redisCache;
    private final TbDdsHandshakeMapper tbDdsHandshakeMapper;
    private final TbDdsTaskScheduleMapper tbDdsTaskScheduleMapper;
    private final TbDdsScriptlabelMapper tbDdsScriptlabelMapper;
    private final TbDdsSiteLogMapper tbDdsSiteLogMapper;
    private final ITbDdsFileService tbDdsFileService;
    private final PdsAppConfig pdsAppConfig;

    /**
     * 查询节点列表
     */
    @Override
    public List<SiteVO> selectSiteList(SiteDTO siteDTO) {
        final List<SiteVO> siteVOS = baseMapper.selectTbDdsSiteList(siteDTO);
        if (siteDTO.isFindHandshakeFlag()) {
            final Integer exceptionDays = siteDTO.getExceptionDays();
            for (SiteVO siteVO : siteVOS) {
                nodeMonitorInfo(siteVO, exceptionDays);
            }
        }
        return siteVOS;
    }

    private void nodeMonitorInfo(final SiteVO vo, final Integer exceptionDays) {
        final Integer siteId = vo.getId();
        final TbDdsHandshake handshake = tbDdsHandshakeMapper.findOne(Wrappers.lambdaQuery(TbDdsHandshake.class)
                .eq(TbDdsHandshake::getSiteId, siteId).orderByDesc(TbDdsHandshake::getSignalDate));

        if (handshake != null) {
            vo.setIp(handshake.getSiteIp());
            vo.setLastHandshakeTime(handshake.getSignalDate());

            final String sysInfo = handshake.getSysInfo();
            if (StrUtil.isNotBlank(sysInfo)) {
                try {
                    // 获取CPU、内存、磁盘信息
                    final SystemInfoDTO systemInfoDTO = JSON.parseObject(sysInfo, SystemInfoDTO.class);
                    vo.setSystemInfoDTO(systemInfoDTO);
                } catch (Exception e) {
                    log.error("解析系统信息出错：", e);
                }
            }

            // 判断节点是否断开的时间间隔，超过3分钟则认为断开
            final long interval = pdsAppConfig.getHandshakeInterval() * 60 * 1000 + 15000L;
            final Calendar c = Calendar.getInstance();
            if ((c.getTimeInMillis() - handshake.getSignalDate().getTime()) > interval) {
                vo.setHeartbeatSignal(SiteRunStatusEnum.disconnect.getText());
            } else {
                int offsetDays = (exceptionDays == null || exceptionDays < 1) ? 1 : exceptionDays;
                offsetDays = Math.min(5, offsetDays);

                // 指定
                LambdaQueryWrapper<TbDdsTaskSchedule> queryWrapper = Wrappers.lambdaQuery(TbDdsTaskSchedule.class)
                        .eq(TbDdsTaskSchedule::getSiteId, siteId)
                        .ge(TbDdsTaskSchedule::getTimeExecute, DateUtil.offsetDay(new Date(), -offsetDays));

                final TbDdsTaskSchedule ddsTaskSchedule = tbDdsTaskScheduleMapper.findOne(queryWrapper);
                if (ddsTaskSchedule != null) {
                    boolean hasSuccess = TaskPaperScheduleStatusEnum.success.name().equals(ddsTaskSchedule.getStatus())
                            || TaskPaperScheduleStatusEnum.ignore.name().equals(ddsTaskSchedule.getStatus());
                    if (!hasSuccess) {
                        queryWrapper = Wrappers.lambdaQuery(TbDdsTaskSchedule.class)
                                .eq(TbDdsTaskSchedule::getSiteId, siteId)
                                .in(TbDdsTaskSchedule::getStatus, CollUtil.toList(TaskPaperScheduleStatusEnum.success.name(), TaskPaperScheduleStatusEnum.ignore.name()))
                                .ge(TbDdsTaskSchedule::getTimeExecute, DateUtil.offsetDay(new Date(), -offsetDays));
                        final TbDdsTaskSchedule successDdsTaskSchedule = tbDdsTaskScheduleMapper.findOne(queryWrapper);
                        hasSuccess = successDdsTaskSchedule != null;
                    }

                    if (hasSuccess) {
                        vo.setHeartbeatSignal(SiteRunStatusEnum.connect.getText());
                    } else {
                        vo.setHeartbeatSignal(SiteRunStatusEnum.exception.getText());
                    }
                } else {
                    vo.setHeartbeatSignal(SiteRunStatusEnum.connect.getText());
                }
            }
        } else {
            vo.setHeartbeatSignal(SiteRunStatusEnum.disconnect.getText());
        }
    }

    /**
     * 根据ID获取节点详情
     */
    @Override
    public SiteVO getSiteById(Integer id) {
        if (id == null) {
            throw new ServiceException("节点ID不能为空");
        }

        // 使用Mapper的getSiteVOById方法获取节点详情
        SiteVO vo = baseMapper.getSiteVOById(id);
        if (vo == null) {
            throw new ServiceException("节点不存在");
        }

        return vo;
    }

    public static String getSiteCacheKey(Integer siteId) {
        return CacheConstants.SITE_INFO_KEY + siteId;
    }

    /**
     * 保存节点（新增或修改）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveSite(final SiteDTO siteDTO, final Long userId) {
        if (siteDTO == null) {
            return AjaxResult.error("参数不能为空");
        }

        if (StrUtil.isBlank(siteDTO.getSiteName())) {
            return AjaxResult.error("节点名称不能为空");
        }

        final String siteType = siteDTO.getSiteType();
        if (StrUtil.isBlank(siteType)) {
            return AjaxResult.error("节点类型不能为空");
        }

        final Integer scriptlabelId = siteDTO.getScriptlabelId();
        if (scriptlabelId == null) {
            return AjaxResult.error("脚本标签不能为空");
        }

//        final List<String> allTypes = ScriptTypeEnum.allCode();
        final List<ScriptTypeEnum> groupByType = ScriptTypeEnum.getGroupByType(siteType);
        if (CollUtil.isEmpty(groupByType)) {
            return AjaxResult.error("节点类型错误");
        }

        final TbDdsScriptlabel scriptlabel = tbDdsScriptlabelMapper.findOne(Wrappers.lambdaQuery(TbDdsScriptlabel.class)
                .eq(TbDdsScriptlabel::getId, scriptlabelId)
                .in(TbDdsScriptlabel::getType, groupByType.stream().map(ScriptTypeEnum::getCode).toList()));
        if (scriptlabel == null) {
            return AjaxResult.error("节点类型和脚本标签类型不匹配");
        }

        /*final String siteGroup = siteDTO.getSiteGroup();
        if (StrUtil.isBlank(siteGroup)) {
            return AjaxResult.error("节点分组不能为空");
        }*/

        try {
            // 检查节点名称是否已存在
            TbDdsSite existingByName = getOne(Wrappers.lambdaQuery(TbDdsSite.class)
                    .eq(TbDdsSite::getSiteName, siteDTO.getSiteName())
                    .ne(siteDTO.getId() != null, TbDdsSite::getId, siteDTO.getId()));

            if (existingByName != null) {
                return AjaxResult.error("节点名称已存在，请更换名称");
            }

            TbDdsSite entity;
            final boolean isUpdate = siteDTO.getId() != null;
            final Date now = new Date();

            if (isUpdate) {
                // 修改操作，先查询原记录
                entity = getById(siteDTO.getId());
                if (entity == null) {
                    return AjaxResult.error("节点不存在");
                }

                // 不允许修改节点类型
                /*if (!entity.getSiteType().equals(siteType)) {
                    return AjaxResult.error("节点类型不允许修改");
                }*/

                entity.setUpdateTime(now);
            } else {
                // 新增操作
                entity = new TbDdsSite();
                entity.setCreator(userId);
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                entity.setStatus(StatusEnums.ENABLE.getCode().toString()); // 默认状态为正常
                entity.setApiToken(pdsAppConfig.getDefaultApiToken());
            }

            // 设置属性
            entity.setSiteName(siteDTO.getSiteName());
            entity.setSiteAbbr(siteDTO.getSiteAbbr());
            entity.setSiteType(siteType);
//            entity.setSiteGroup(siteGroup);
            entity.setObtainTaskInterval(siteDTO.getObtainTaskInterval());
            entity.setUnit(siteDTO.getUnit());
            entity.setAddress(siteDTO.getAddress());

            entity.setScriptlabelId(scriptlabelId);
            final Integer taskThreadNum = siteDTO.getTaskThreadNum();
            entity.setTaskThreadNum(taskThreadNum == null ? 10 : taskThreadNum);

            // 如果更新时传入了状态，则使用传入的状态
            if (isUpdate && StrUtil.isNotBlank(siteDTO.getStatus())) {
                entity.setStatus(siteDTO.getStatus());
            }

            // 保存记录
            boolean success = saveOrUpdate(entity);
            cleanSiteCache();
            if (!success) {
                return AjaxResult.error(isUpdate ? "修改失败" : "添加失败");
            }

            return AjaxResult.success(isUpdate ? "修改成功" : "添加成功", entity.getId());
        } catch (Exception e) {
            log.error("保存节点失败", e);
            return AjaxResult.error("操作失败：" + e.getMessage());
        }
    }

    private void cleanSiteCache() {
        Collection<String> keys = redisCache.keys(CacheConstants.SITE_INFO_KEY + "*");
        if (CollUtil.isNotEmpty(keys)) {
            redisCache.deleteObject(keys);
        }
    }

    /**
     * 删除节点
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteSiteById(Integer id) {
        if (id == null) {
            return AjaxResult.error("节点ID不能为空");
        }

        try {
            // 查询节点信息
            TbDdsSite site = getById(id);
            if (site == null) {
                return AjaxResult.error("节点不存在");
            }

            // 删除节点记录
            boolean success = removeById(id);
            cleanSiteCache();
            if (!success) {
                throw new ServiceException("删除失败");
            }

            return AjaxResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除节点失败", e);
            throw new ServiceException("删除失败：" + e.getMessage());
        }
    }

    /**
     * 修改节点状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult changeStatus(SiteStatusDTO statusDTO) {
        if (statusDTO == null || statusDTO.getSiteId() == null || StrUtil.isBlank(statusDTO.getStatus())) {
            return AjaxResult.error("参数不能为空");
        }

        try {
            // 查询节点信息
            TbDdsSite entity = getById(statusDTO.getSiteId());
            if (entity == null) {
                return AjaxResult.error("节点不存在");
            }

            // 修改状态
            entity.setStatus(statusDTO.getStatus());
            entity.setUpdateTime(new Date());

            // 保存记录
            boolean success = updateById(entity);
            cleanSiteCache();
            if (!success) {
                return AjaxResult.error("状态修改失败");
            }

            return AjaxResult.success("状态修改成功");
        } catch (Exception e) {
            log.error("修改节点状态失败", e);
            return AjaxResult.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有节点分组
     */
    @Override
    public List<String> selectAllGroups() {
        // 查询所有节点
        /*List<TbDdsSite> sites = list();
        // 提取所有非空分组并去重
        return sites.stream()
                .map(TbDdsSite::getSiteGroup)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());*/
        return new ArrayList<>();
    }

    @Override
    public TbDdsSite findByIdWithCache(Integer siteId) {
        if (siteId == null) {
            return null;
        }
        final String cacheKey = getSiteCacheKey(siteId);
        TbDdsSite site = redisCache.getCacheObject(cacheKey);
        if (site == null) {
            site = getById(siteId);
            redisCache.setCacheObject(cacheKey, site, 60, TimeUnit.MINUTES);
        }
        return site;
    }

    @Override
    public List<Integer> findReTrySiteIds(final Long paperId, final List<String> supportSiteType) {
        if (paperId == null || CollUtil.isEmpty(supportSiteType)) {
            return null;
        }
        // 获取当前时间往前推3分钟的时间字符串
        final String aliveHandshakeTime = initAliveHandshakeTime(pdsAppConfig.getHandshakeInterval());
        final String batchType = ScriptTypeEnum.batch.getCode();
        return this.baseMapper.findReTrySiteIds(StatusEnums.ENABLE.getCode().toString(), aliveHandshakeTime, paperId, supportSiteType, batchType);
    }

    public static String initAliveHandshakeTime(final Integer handshakeInterval) {
        // 获取当前时间往前推3分钟的时间字符串
        int intervalVal = 3;
        if (handshakeInterval != null && handshakeInterval > 0) {
            intervalVal = handshakeInterval;
        }
        // 获取当前时间往前推3分钟的时间字符串
        return DateFormatUtils.format(DateUtil.offsetMinute(new Date(), -intervalVal), DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * 添加站点统计信息
     */
    @Override
    public SiteVO addStatInfo(SiteVO siteVO) {
        if (siteVO != null) {
            final Integer id = siteVO.getId();
            final List<StatInfoVO> statInfoVOS = tbDdsTaskScheduleMapper.countGroupByStatusOfSite(id);

            final Map<String, Integer> map = new HashMap<>();
            if (CollUtil.isNotEmpty(statInfoVOS)) {
                for (StatInfoVO statInfoVO : statInfoVOS) {
                    map.put(statInfoVO.getStatus(), statInfoVO.getNum());
                }
            }
            siteVO.setFailed(map.getOrDefault(TaskPaperScheduleStatusEnum.failed.name(), 0));
            siteVO.setSuccess(map.getOrDefault(TaskPaperScheduleStatusEnum.success.name(), 0));
            siteVO.setExecute(map.getOrDefault(TaskPaperScheduleStatusEnum.executing.name(), 0));
            siteVO.setTotal(map.values().stream().mapToInt(value -> value).sum());
        }
        return siteVO;
    }

    @Override
    public void exportNodeMonitor(HttpServletResponse response) {
        // 查询所有节点数据
        SiteDTO siteDTO = new SiteDTO();
        siteDTO.setFindHandshakeFlag(true);
        List<SiteVO> siteList = selectSiteList(siteDTO);

        if (CollUtil.isEmpty(siteList)) {
            throw new ServiceException("暂无节点数据可导出");
        }

        // 获取所有节点ID，用于批量查询任务统计
        final Set<Integer> siteIds = siteList.stream()
                .map(SiteVO::getId)
                .collect(Collectors.toSet());

        // 批量获取任务统计数据
        List<StatInfoVO> taskStats = tbDdsTaskScheduleMapper.countGroupByStatusOfSites(siteIds);
        Map<Integer, Map<String, Integer>> taskStatsMap = new HashMap<>();

        // 按站点ID分组统计数据
        for (StatInfoVO stat : taskStats) {
            taskStatsMap.computeIfAbsent(stat.getSiteId(), k -> new HashMap<>())
                    .put(stat.getStatus(), stat.getNum());
        }

        // 转换为导出DTO
        List<NodeMonitorExportDTO> exportList = new ArrayList<>();
        for (SiteVO site : siteList) {
            NodeMonitorExportDTO exportDTO = convertToExportDTO(site, taskStatsMap.get(site.getId()));
            exportList.add(exportDTO);
        }

        // 使用ExcelUtil导出
        ExcelUtil<NodeMonitorExportDTO> util = new ExcelUtil<>(NodeMonitorExportDTO.class);
        util.exportExcel(response, exportList, "节点监控数据");
    }

    /**
     * 转换为导出DTO
     */
    private NodeMonitorExportDTO convertToExportDTO(SiteVO site, Map<String, Integer> taskStats) {
        NodeMonitorExportDTO dto = new NodeMonitorExportDTO();

        // 基本信息
        dto.setId(site.getId());
        dto.setSiteName(site.getSiteName());
        dto.setSiteType(site.getSiteType());
//        dto.setSiteGroup(site.getSiteGroup());
        dto.setLabelName(site.getLabelName());
        dto.setStatus(site.getStatus());
        dto.setIp(site.getIp());
        dto.setLastHandshakeTime(site.getLastHandshakeTime());
        dto.setHeartbeatSignal(site.getHeartbeatSignal());
//        dto.setTaskThreadNum(site.getTaskThreadNum());
        dto.setObtainTaskInterval(site.getObtainTaskInterval());
        dto.setCreateTime(site.getCreateTime());

        // 系统资源信息
        SystemInfoDTO systemInfo = site.getSystemInfoDTO();
        if (systemInfo != null) {
            // CPU信息
            if (systemInfo.getCpu() != null) {
                dto.setCpuLogicalCount(systemInfo.getCpu().getLogicalCount());
                dto.setCpuPhysicalCount(systemInfo.getCpu().getPhysicalCount());
                dto.setCpuFreqCurrent(systemInfo.getCpu().getFreqCurrent());
            }

            // 内存信息
            if (systemInfo.getMemory() != null) {
                SystemInfoDTO.MemoryInfoDTO memory = systemInfo.getMemory();
                dto.setMemoryTotal(bytesToGB(memory.getTotal()));
                dto.setMemoryUsed(bytesToGB(memory.getUsed()));
                dto.setMemoryPercent(memory.getPercent());
            }

            // 磁盘信息
            if (systemInfo.getDisk() != null) {
                SystemInfoDTO.DiskInfoDTO disk = systemInfo.getDisk();
                dto.setDiskTotal(bytesToGB(disk.getTotal()));
                dto.setDiskUsed(bytesToGB(disk.getUsed()));
                dto.setDiskPercent(disk.getPercent());
            }
        }

        // 任务统计信息
        if (taskStats != null) {
            dto.setExecutingTasks(taskStats.getOrDefault(TaskPaperScheduleStatusEnum.executing.name(), 0));
            dto.setSuccessTasks(taskStats.getOrDefault(TaskPaperScheduleStatusEnum.success.name(), 0));
            dto.setFailedTasks(taskStats.getOrDefault(TaskPaperScheduleStatusEnum.failed.name(), 0));
//            dto.setIgnoreTasks(taskStats.getOrDefault(TaskPaperScheduleStatusEnum.ignore.name(), 0));
            dto.setTotalTasks(taskStats.values().stream().mapToInt(Integer::intValue).sum());
        } else {
            dto.setExecutingTasks(0);
            dto.setSuccessTasks(0);
            dto.setFailedTasks(0);
//            dto.setIgnoreTasks(0);
            dto.setTotalTasks(0);
        }

        return dto;
    }

    /**
     * 字节转GB
     */
    private Double bytesToGB(Long bytes) {
        if (bytes == null || bytes <= 0) {
            return 0.0;
        }
        return bytes / (1024.0 * 1024.0 * 1024.0);
    }

    /**
     * 获取站点日志列表
     */
    @Override
    public AjaxResult getSiteLogList(Integer siteId) {
        try {
            final TbDdsSite site = this.getById(siteId);
            if (site == null) {
                throw new ServiceException("站点不存在");
            }
            // 查询站点日志，按创建时间降序排序
            final List<TbDdsSiteLog> logList = tbDdsSiteLogMapper.selectList(
                    Wrappers.lambdaQuery(TbDdsSiteLog.class)
                            .eq(TbDdsSiteLog::getSiteId, siteId)
                            .orderByDesc(TbDdsSiteLog::getCreateTime)
            );
            final SiteLogVO vo = new SiteLogVO();
            vo.setLogList(logList);

            final Integer toRefreshLog = site.getToRefreshLog();
            vo.setToRefreshLog(toRefreshLog == null ? 0 : toRefreshLog);

            return AjaxResult.success(vo);
        } catch (Exception e) {
            log.error("获取站点日志列表失败：siteId={}", siteId, e);
            return AjaxResult.error("获取日志列表失败：" + e.getMessage());
        }
    }

    /**
     * 下载站点日志文件
     */
    @Override
    public void downloadSiteLog(Long logId, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 查询日志记录
            TbDdsSiteLog siteLog = tbDdsSiteLogMapper.selectById(logId);
            if (siteLog == null) {
                throw new ServiceException("日志记录不存在");
            }

            // 获取文件信息
            TbDdsFile file = tbDdsFileService.getById(siteLog.getFileId());
            if (file == null) {
                throw new ServiceException("日志文件不存在");
            }

            // 下载文件
            String fileName = String.format("site_%d_%s.log",
                    siteLog.getSiteId(),
                    DateFormatUtils.format(siteLog.getCreateTime(), "yyyyMMdd_HHmmss"));
            tbDdsFileService.download(siteLog.getFileId(), fileName, request, response);
        } catch (Exception e) {
            log.error("下载站点日志文件失败：logId={}", logId, e);
            throw new ServiceException("下载日志文件失败：" + e.getMessage());
        }
    }

    /**
     * 刷新站点日志（设置to_refresh_log为1）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult refreshSiteLog(Integer siteId) {
        try {
            // 查询站点是否存在
            TbDdsSite site = getById(siteId);
            if (site == null) {
                return AjaxResult.error("站点不存在");
            }

            // 检查当前状态
            if (Integer.valueOf(1).equals(site.getToRefreshLog())) {
                return AjaxResult.error("日志刷新请求已在处理中，请稍后再试");
            }

            // 更新to_refresh_log字段为1
            site.setToRefreshLog(1);
            site.setUpdateTime(new Date());
            boolean success = updateById(site);

            if (success) {
                // 清除缓存
                cleanSiteCache();
                log.info("站点日志刷新请求已发送：siteId={}", siteId);
                return AjaxResult.success("日志刷新请求已发送");
            } else {
                return AjaxResult.error("刷新日志失败");
            }

        } catch (Exception e) {
            log.error("刷新站点日志失败：siteId={}", siteId, e);
            return AjaxResult.error("刷新日志失败：" + e.getMessage());
        }
    }

}
