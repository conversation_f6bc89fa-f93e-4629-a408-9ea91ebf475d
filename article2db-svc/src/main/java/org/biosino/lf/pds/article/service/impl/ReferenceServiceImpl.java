package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.Reference;
import org.biosino.lf.pds.article.mapper.ReferenceMapper;
import org.biosino.lf.pds.article.service.IReferenceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 参考文献信息表 服务实现类
 */
@Slf4j
@Service
public class ReferenceServiceImpl extends ServiceImpl<ReferenceMapper, Reference> implements IReferenceService {
    @Override
    public List<Reference> findByDocId(Long docId) {
        return this.list(
                Wrappers.<Reference>lambdaQuery()
                        .eq(Reference::getDocId, docId)
        );
    }

    @Override
    public boolean removeByDocId(Long id) {
        return this.remove(Wrappers.<Reference>lambdaQuery()
                .eq(Reference::getDocId, id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReferenceCitations(List<Reference> refs) {
        if (CollUtil.isEmpty(refs)) {
            return;
        }

        try {
            // 由于Citus分布式表的限制，不能使用updateBatchById批量更新
            // 改为单独更新每个记录的citation、pmid、pmcid、doi字段
            for (Reference ref : refs) {
                if (ref.getId() != null) {
                    // 只更新非分区键字段：citation, pmid, pmcid, doi
                    LambdaUpdateWrapper<Reference> updateWrapper = Wrappers.<Reference>lambdaUpdate()
                            .eq(Reference::getId, ref.getId())
                            .set(StrUtil.isNotBlank(ref.getCitation()), Reference::getCitation, ref.getCitation())
                            .set(StrUtil.isNotBlank(ref.getPmid()), Reference::getPmid, ref.getPmid())
                            .set(StrUtil.isNotBlank(ref.getPmcid()), Reference::getPmcid, ref.getPmcid())
                            .set(StrUtil.isNotBlank(ref.getDoi()), Reference::getDoi, ref.getDoi());

                    this.update(updateWrapper);
                }
            }
            log.info("批量更新引用格式完成，共更新{}条记录", refs.size());
        } catch (Exception e) {
            log.error("批量更新引用格式失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新引用信息失败: " + e.getMessage(), e);
        }
    }

}
