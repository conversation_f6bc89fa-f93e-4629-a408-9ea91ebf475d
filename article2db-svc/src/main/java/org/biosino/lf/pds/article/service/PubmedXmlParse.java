package org.biosino.lf.pds.article.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.dto.ArticleInfoDTO;
import org.biosino.lf.pds.common.enums.DateTypeEnums;
import org.biosino.lf.pds.common.enums.JournalSourceTypeEnums;
import org.biosino.lf.pds.common.enums.SourceTypeEnums;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.MonthUtil;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.parser.Parser;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/5/12
 */
@Service
public class PubmedXmlParse implements XmlParseService {

    @Override
    public ArticleInfoDTO parse(String content, SourceTypeEnums typeEnums) {
        if (StrUtil.isBlank(content)) {
            throw new ServiceException("XML文件内容为空");
        }

//        if (!XmlUtils.isWellFormedXML(content)) {
//            throw new ServiceException("XML格式不合法");
//        }

        ArticleInfoDTO articleInfoDTO = new ArticleInfoDTO();
        Document doc = Jsoup.parse(content, "", Parser.xmlParser());

        // 解析基本信息
        Article article = parseBasicInfo(doc);
        articleInfoDTO.setArticle(article);

        // 解析期刊信息
        articleInfoDTO.setJournal(parseJournal(doc));

        // 解析数据库信息
        articleInfoDTO.setArticleDatabanks(parseDatabank(doc));

        // 解析发布类型
        articleInfoDTO.setPubTypes(parsePubTypes(doc));

        // 解析化学物质
        articleInfoDTO.setChemicals(parseChemicals(doc));

        // 解析MeSH术语
        articleInfoDTO.setArticleMeshes(parseMeshHeadings(doc));

        // 解析补充MeSH术语
        articleInfoDTO.setArticleSupplMeshes(parseSupplMeshList(doc));

        // 解析基因信息
        articleInfoDTO.setGenes(parseGenes(doc));

        // 解析资助信息
        articleInfoDTO.setGrants(parseGrants(doc));

        // 其他id
        articleInfoDTO.setArticleOtherIds(parseArticleOtherId(doc));

        // 解析引用文献
        articleInfoDTO.setReferences(parseReferences(doc));

        articleInfoDTO.setArticleAuthors(parseArticleAuthor(doc));

        return articleInfoDTO;
    }

    private Journal parseJournal(Document doc) {
        Journal journal = new Journal();
        journal.setSource(CollUtil.newArrayList(SourceTypeEnums.PubMed.name()));
        journal.setSourceType(JournalSourceTypeEnums.system.name());

        Elements journalEles = doc.select("PubmedArticle > MedlineCitation > Article > Journal");

        Elements nlmIdEles = doc.select("PubmedArticle > MedlineCitation > MedlineJournalInfo > NlmUniqueID");
        if (StrUtil.isNotBlank(nlmIdEles.text())) {
            journal.setUniqueNlmId(StrUtil.trimToNull(nlmIdEles.text()));
        }

        Elements titleEles = journalEles.select("Title");
        journal.setTitle(StrUtil.trimToNull(titleEles.text()));

        Elements isoEles = journalEles.select("ISOAbbreviation");
        if (StrUtil.isNotBlank(isoEles.text())) {
            journal.setIsoabbreviation(StrUtil.trimToNull(isoEles.text()));
        }

        Elements issnEles = journalEles.select("ISSN");

        if (!issnEles.isEmpty()) {
            String issnType = issnEles.attr("IssnType");

            if (StrUtil.isNotBlank(issnEles.text())) {
                if ("Print".equals(issnType)) {
                    journal.setIssnPrint(StrUtil.trimToNull(issnEles.text()));
                } else if ("Electronic".equals(issnType)) {
                    journal.setIssnElectronic(StrUtil.trimToNull(issnEles.text()));
                }
            }
        }

        return journal;
    }

    private Article parseBasicInfo(Document doc) {
        Article article = new Article();
        Elements pubmedArticle = doc.select("PubmedArticleSet > PubmedArticle");

        if (pubmedArticle == null) {
            throw new ServiceException("XML中未找到PubmedArticle标签");
        }

        if (pubmedArticle.size() > 1) {
            throw new ServiceException("XML中存在" + pubmedArticle.size() + "篇文献");
        }

        // 解析PMID
        Elements pmidEles = doc.select("PubmedArticle > MedlineCitation > PMID");
        String pmid = StrUtil.trimToNull(pmidEles.text());
        if (StrUtil.isBlank(pmid)) {
            throw new ServiceException("PMID 为空");
        }
        if (!StrUtil.isNumeric(pmid)) {
            throw new ServiceException("PMID 不是数字");
        }
        article.setPmid(Long.parseLong(pmid));

        Elements doiEles = doc.select("PubmedArticle > PubmedData > ArticleIdList > ArticleId[IdType=doi]");
        article.setDoi(StrUtil.trimToNull(doiEles.text()));

        Elements pmcEles = doc.select("PubmedArticle > PubmedData > ArticleIdList > ArticleId[IdType=pmc]");
        if (CollUtil.isNotEmpty(pmcEles)) {
            String pmcStr = StrUtil.trimToNull(pmcEles.text()).replace("PMC", "");
            // 兼容PMC3148528.1情况
            if (pmcStr.contains(".")) {
                pmcStr = pmcStr.substring(0, pmcStr.indexOf("."));
            }

            if (StrUtil.isNumeric(pmcStr)) {
                article.setPmcId(Long.parseLong(pmcStr));
//            } else {
//                throw new ServiceException("PMCID 不是数字");
            }
        }

        article.setSource(CollUtil.newArrayList(SourceTypeEnums.PubMed.name()));

        // 解析发布状态
        Elements pubStaEles = doc.select("PubmedArticle > PubmedData > PublicationStatus");
        article.setPubStatus(StrUtil.trimToNull(pubStaEles.text()));

        // 解析语言
        Elements languageEles = doc.select("PubmedArticle > MedlineCitation > Article > Language");
        article.setLanguage(StrUtil.trimToNull(languageEles.text()));

        // 解析标题
        Elements titleEles = doc.select("PubmedArticle > MedlineCitation > Article > ArticleTitle");
        String title = titleEles.text();
        if (StrUtil.isNotBlank(title)) {
            title = HtmlUtil.removeHtmlTag(titleEles.html(), "fn");
            title = title.replaceAll("\\s+", " ");
            article.setTitle(StrUtil.trimToNull(title));
        }

        // 解析本地标题
        Elements vernacularTitleEles = doc.select("PubmedArticle > MedlineCitation > Article > VernacularTitle");
        article.setVernacularTitle(StrUtil.trimToNull(vernacularTitleEles.text()));

        // 发布日期
        Elements publishedDateEles = doc.select("PubmedArticle > MedlineCitation > Article > Journal > JournalIssue > PubDate");
        String publishedDateYear = publishedDateEles.select("Year").text();
        String publishedDateMonth = publishedDateEles.select("Month").text();
        String publishedDateDay = publishedDateEles.select("Day").text();

        if (StrUtil.isNotBlank(publishedDateYear)) {
            article.setPublishedYear(Integer.parseInt(publishedDateYear));
        }
        if (StrUtil.isNotBlank(publishedDateMonth)) {
            article.setPublishedMonth(MonthUtil.getMonth(publishedDateMonth));
        }
        if (StrUtil.isNotBlank(publishedDateDay)) {
            article.setPublishedDay(Integer.parseInt(publishedDateDay));
        }

        // 设置年份
        if (article.getPublishedYear() != null) {
            article.setYear(article.getPublishedYear());
        }

        // 解析其他日期
        List<PubMedPubDate> items = new ArrayList<>();

        // 解析ArticleDate标签
        Elements articleDateEles = doc.select("PubmedArticle > MedlineCitation > Article > ArticleDate");
        if (CollUtil.isNotEmpty(articleDateEles)) {
            for (Element articleDateEle : articleDateEles) {
                PubMedPubDate item = new PubMedPubDate();
                String dateType = StrUtil.trimToNull(articleDateEle.attr("DateType"));
                // 将DateType="Electronic"映射为PubStatus="epub"
                String pubStatus = "Electronic".equals(dateType) ? "epub" : dateType;

                if (!DateTypeEnums.existByType(pubStatus)) {
                    continue;
                }

                item.setPubStatus(pubStatus);
                item.setYear(StrUtil.trimToNull(articleDateEle.select("Year").text()));
                item.setMonth(StrUtil.trimToNull(articleDateEle.select("Month").text()));
                item.setDay(StrUtil.trimToNull(articleDateEle.select("Day").text()));
                items.add(item);
            }
        }

        // 解析PubMedPubDate标签
        Elements pubMedPubDateEles = doc.select("PubmedArticle > PubmedData > History > PubMedPubDate");
        if (CollUtil.isNotEmpty(pubMedPubDateEles)) {
            for (Element publishedDateEle : pubMedPubDateEles) {
                PubMedPubDate item = new PubMedPubDate();
                String pubStatus = StrUtil.trimToNull(publishedDateEle.attr("PubStatus"));
                if (!DateTypeEnums.existByType(pubStatus)) {
                    continue;
                }
                item.setPubStatus(pubStatus);
                item.setYear(StrUtil.trimToNull(publishedDateEle.select("Year").text()));
                item.setMonth(StrUtil.trimToNull(publishedDateEle.select("Month").text()));
                item.setDay(StrUtil.trimToNull(publishedDateEle.select("Day").text()));
                items.add(item);
            }
        }

        if (!items.isEmpty()) {
            article.setOtherDate(items);
        } else {
            // 确保为空时设置为null而不是空列表
            article.setOtherDate(null);
        }

        // 解析卷号
        Elements volumeEles = doc.select("PubmedArticle > MedlineCitation > Article > Journal > JournalIssue > Volume");
        article.setVolume(StrUtil.trimToNull(volumeEles.text()));

        // 解析期号
        Elements issueEles = doc.select("PubmedArticle > MedlineCitation > Article > Journal > JournalIssue > Issue");
        article.setIssue(StrUtil.trimToNull(issueEles.text()));

        // 解析页码
        Elements pageEles = doc.select("PubmedArticle > MedlineCitation > Article > Pagination > MedlinePgn");
        article.setPage(StrUtil.trimToNull(pageEles.text()));

        // 解析作者
        Elements authorEles = doc.select("PubmedArticle > MedlineCitation > Article > AuthorList > Author");
        if (CollUtil.isNotEmpty(authorEles)) {
            List<String> authors = new ArrayList<>();
            for (Element ele : authorEles) {
                String foreName = ele.select("ForeName").text();
                String lastName = ele.select("LastName").text();
                String fullName = foreName + " " + lastName;
                authors.add(fullName);
            }
            if (CollUtil.isNotEmpty(authors)) {
                article.setAuthor(CollUtil.distinct(authors));
            }
        }

        // 解析机构信息
        Elements affiliationEles = doc.select("PubmedArticle > MedlineCitation > Article > AuthorList > Author > AffiliationInfo > Affiliation");
        if (CollUtil.isNotEmpty(affiliationEles)) {
            List<String> affiliations = new ArrayList<>();
            for (Element ele : affiliationEles) {
                affiliations.add(ele.text());
            }
            if (CollUtil.isNotEmpty(affiliations)) {
                article.setAffiliation(CollUtil.distinct(affiliations));
            }
        }

        // 解析摘要
        Elements absEles = doc.select("PubmedArticle > MedlineCitation > Article > Abstract > AbstractText");
        if (CollUtil.isNotEmpty(absEles)) {
            StringBuilder sb = new StringBuilder();
            for (Element ele : absEles) {
                String label = ele.attr("Label");
                String content = ele.text();
                if (StrUtil.isNotBlank(label)) {
                    sb.append("<label>").append(label).append(": </label>");
                }
                sb.append(content);
                sb.append("\n");
            }
            article.setArticleAbstract(sb.toString());
        }

        // 解析版权信息
        Elements cprEles = doc.select("PubmedArticle > MedlineCitation > Article > Abstract > CopyrightInformation");
        article.setCopyright(StrUtil.trimToNull(cprEles.text()));

        // 解析其他摘要
        Elements otherAbsEles = doc.select("PubmedArticle > MedlineCitation > OtherAbstract > AbstractText");
        if (CollUtil.isNotEmpty(otherAbsEles)) {
            StringBuilder sb = new StringBuilder();
            for (Element ele : otherAbsEles) {
                String label = ele.attr("Label");
                String content = ele.text();
                if (StrUtil.isNotBlank(label)) {
                    sb.append("<label>").append(label).append("</label>");
                }
                sb.append(content);
                sb.append("\n");
            }
            article.setOtherAbstract(sb.toString());
        }

        // 解析关键词
        Elements keywordEles = doc.select("PubmedArticle > MedlineCitation > KeywordList > Keyword");
        if (CollUtil.isNotEmpty(keywordEles)) {
            List<String> keywords = new ArrayList<>();
            for (Element ele : keywordEles) {
                String kyw = StrUtil.trimToNull(ele.text());
                if (StrUtil.isNotBlank(kyw) && !".".equals(kyw)) {
                    keywords.add(kyw);
                }
            }
            if (CollUtil.isNotEmpty(keywords)) {
                article.setKeywords(keywords);
            }
        }

        return article;
    }

    /**
     * 解析数据库信息
     */
    private Set<ArticleDatabank> parseDatabank(Document doc) {
        Set<ArticleDatabank> databanks = new LinkedHashSet<>();
        Elements databankEles = doc.select("PubmedArticle > MedlineCitation > Article > DataBankList > DataBank");

        for (Element ele : databankEles) {
            String databankName = ele.select("DataBankName").text();
            Elements accNumberEles = ele.select("AccessionNumberList > AccessionNumber");

            for (Element accEle : accNumberEles) {
                ArticleDatabank databank = new ArticleDatabank();
                databank.setName(databankName);
                databank.setValue(accEle.text());
                databanks.add(databank);
            }
        }

        return databanks;
    }

    /**
     * 解析发布类型
     */
    private Set<PubType> parsePubTypes(Document doc) {
        Set<PubType> pubTypes = new LinkedHashSet<>();
        Elements pubTypeEles = doc.select("PubmedArticle > MedlineCitation > Article > PublicationTypeList > PublicationType");

        for (Element ele : pubTypeEles) {
            PubType pubType = new PubType();
            pubType.setPubType(StrUtil.trimToNull(ele.text()));
            pubTypes.add(pubType);
        }

        return pubTypes;
    }

    /**
     * 解析化学物质
     */
    private Set<Chemical> parseChemicals(Document doc) {
        Set<Chemical> chemicals = new LinkedHashSet<>();
        Elements chemicalEles = doc.select("PubmedArticle > MedlineCitation > ChemicalList > Chemical");

        for (Element ele : chemicalEles) {
            Chemical chemical = new Chemical();
            chemical.setName(StrUtil.trimToNull(ele.select("NameOfSubstance").text()));
            chemical.setRegistryNo(StrUtil.trimToNull(ele.select("RegistryNumber").text()));
            chemical.setMeshId(StrUtil.trimToNull(ele.select("NameOfSubstance").attr("UI")));
            chemicals.add(chemical);
        }

        return chemicals;
    }

    /**
     * 解析MeSH术语
     */
    private Set<ArticleMesh> parseMeshHeadings(Document doc) {
        Set<ArticleMesh> meshes = new LinkedHashSet<>();
        Elements meshEles = doc.select("PubmedArticle > MedlineCitation > MeshHeadingList > MeshHeading");

        for (Element ele : meshEles) {
            ArticleMesh mesh = new ArticleMesh();
            mesh.setDescriptorId(StrUtil.trimToNull(ele.select("DescriptorName").attr("UI")));

            // 处理限定词
            Elements qualifierEles = ele.select("QualifierName");
            if (CollUtil.isNotEmpty(qualifierEles)) {
                mesh.setQualifierId1(StrUtil.trimToNull(qualifierEles.get(0).attr("UI")));
                if (qualifierEles.size() >= 2) {
                    mesh.setQualifierId2(StrUtil.trimToNull(qualifierEles.get(1).attr("UI")));
                }
                if (qualifierEles.size() >= 3) {
                    mesh.setQualifierId3(StrUtil.trimToNull(qualifierEles.get(2).attr("UI")));
                }
            }

            meshes.add(mesh);
        }

        return meshes;
    }

    /**
     * 解析补充MeSH术语
     */
    private Set<ArticleSupplMesh> parseSupplMeshList(Document doc) {
        Set<ArticleSupplMesh> supplMeshes = new LinkedHashSet<>();
        Elements supplMeshEles = doc.select("PubmedArticle > MedlineCitation > SupplMeshList > SupplMeshName");

        for (Element ele : supplMeshEles) {
            ArticleSupplMesh mesh = new ArticleSupplMesh();
            mesh.setSupplmeshId(StrUtil.trimToNull(ele.attr("UI")));
            mesh.setName(StrUtil.trimToNull(ele.text()));
            mesh.setType(StrUtil.trimToNull(ele.attr("Type")));
            supplMeshes.add(mesh);
        }
        return supplMeshes;
    }

    /**
     * 解析基因信息
     */
    private Set<Gene> parseGenes(Document doc) {
        Set<Gene> genes = new LinkedHashSet<>();

        // 示例：从化学物质中识别基因
        Elements geneEles = doc.select("PubmedArticle > MedlineCitation > GeneSymbolList > GeneSymbol");
        for (Element ele : geneEles) {
            Gene gene = new Gene();
            gene.setGeneSymbol(StrUtil.trimToNull(ele.text()));
            genes.add(gene);
        }

        return genes;
    }

    /**
     * 解析资助信息
     */
    private Set<Grant> parseGrants(Document doc) {
        Set<Grant> grants = new LinkedHashSet<>();
        Elements grantEles = doc.select("PubmedArticle > MedlineCitation > Article > GrantList > Grant");

        for (Element ele : grantEles) {
            Grant grant = new Grant();
            grant.setGrantId(StrUtil.trimToNull(ele.select("GrantID").text()));
            grant.setAcronym(StrUtil.trimToNull(ele.select("Acronym").text()));
            grant.setAgency(StrUtil.trimToNull(ele.select("Agency").text()));
            grant.setCountry(StrUtil.trimToNull(ele.select("Country").text()));
            grants.add(grant);
        }

        return grants;
    }


    /**
     * 解析引用文献
     */
    private Set<Reference> parseReferences(Document doc) {
        Set<Reference> references = new LinkedHashSet<>();
        Elements refEles = doc.select("PubmedArticle > PubmedData > ReferenceList > Reference");

        for (Element ele : refEles) {
            Reference reference = new Reference();
            String citation = StrUtil.trimToNull(ele.select("Citation").text());
            if (StrUtil.isBlank(citation)){
                continue;
            }
            reference.setCitation(citation);

            // 解析引用的文章ID
            Elements articleIdEles = ele.select("ArticleIdList > ArticleId");
            if (CollUtil.isNotEmpty(articleIdEles)) {
                for (Element idEle : articleIdEles) {
                    String idType = idEle.attr("IdType");
                    String idValue = StrUtil.trimToNull(idEle.text());

                    switch (idType) {
                        case "pubmed":
                            reference.setPmid(idValue);
                            break;
                        case "doi":
                            reference.setDoi(idValue);
                            break;
                        case "pmc":
                            reference.setPmcid(idValue);
                            break;
                        default:
                            break;
                    }
                }
            }

            references.add(reference);
        }

        return references;
    }


    private Set<ArticleOtherId> parseArticleOtherId(Document doc) {
        Map<String, ArticleOtherId> articleOtherids = new LinkedHashMap<>();

        Elements urlEles = doc.select("PubmedArticle > MedlineCitation > Article > ELocationID");
        if (CollUtil.isNotEmpty(urlEles)) {
            for (Element ele : urlEles) {
                ArticleOtherId articleMesh = new ArticleOtherId();
                articleMesh.setSource(StrUtil.trimToNull(ele.attr("EIdType")));
                articleMesh.setOtherId(StrUtil.trimToNull(ele.text()));
                articleOtherids.put(articleMesh.getSource() + articleMesh.getOtherId(), articleMesh);
            }
        }

        Elements otheridEles = doc.select("PubmedArticle > MedlineCitation > OtherID");
        if (CollUtil.isNotEmpty(otheridEles)) {
            for (Element ele : otheridEles) {
                ArticleOtherId articleMesh = new ArticleOtherId();
                articleMesh.setSource(StrUtil.trimToNull(ele.attr("Source")));
                articleMesh.setOtherId(StrUtil.trimToNull(ele.text()));
                articleOtherids.put(articleMesh.getSource() + articleMesh.getOtherId(), articleMesh);
            }
        }

        Elements aidEles = doc.select("PubmedArticle > PubmedData > ArticleIdList > ArticleId");
        if (CollUtil.isNotEmpty(aidEles)) {
            for (Element ele : aidEles) {
                ArticleOtherId articleMesh = new ArticleOtherId();
                articleMesh.setSource(StrUtil.trimToNull(ele.attr("IdType")));
                articleMesh.setOtherId(StrUtil.trimToNull(ele.text()));
                articleOtherids.put(articleMesh.getSource() + articleMesh.getOtherId(), articleMesh);
            }
        }

        // 很多othorid超级长，是一段大文本，明显不是othorid故舍弃
        Set<ArticleOtherId> results = new LinkedHashSet<ArticleOtherId>();
        for (ArticleOtherId articleOtherid : articleOtherids.values()) {
            if (!StrUtil.isBlank(articleOtherid.getOtherId()) && articleOtherid.getOtherId().length() < 1000) {
                results.add(articleOtherid);
            }
        }
        return results;
    }

    private Set<ArticleAuthor> parseArticleAuthor(Document doc) {
        Set<ArticleAuthor> result = new LinkedHashSet<>();
        Elements authorEles = doc.select("PubmedArticle > MedlineCitation > Article > AuthorList > Author");

        if (CollUtil.isNotEmpty(authorEles)) {
            int idx = 1;
            for (Element authorEle : authorEles) {
                ArticleAuthor articleAuthor = new ArticleAuthor();
                String lastName = authorEle.select("LastName").text();
                String foreName = authorEle.select("ForeName").text();

                String organizationName = authorEle.select("AffiliationInfo > Affiliation").text();

                Author author = new Author();
                author.setLastname(StrUtil.trimToNull(lastName));
                author.setForename(StrUtil.trimToNull(foreName));
                author.setType("person");

                if (StrUtil.isNotBlank(organizationName)) {
                    Set<Organization> organizations = new LinkedHashSet<>();
                    Organization organization = new Organization();
                    organization.setName(organizationName);
                    organizations.add(organization);
                    articleAuthor.setOrganizations(organizations);
                }

                articleAuthor.setAuthor(author);
                articleAuthor.setAuthorOrder(idx++);
                result.add(articleAuthor);
            }
        }
        return result;
    }

}
