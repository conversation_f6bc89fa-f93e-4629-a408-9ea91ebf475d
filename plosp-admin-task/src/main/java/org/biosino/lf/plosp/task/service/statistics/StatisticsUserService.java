package org.biosino.lf.plosp.task.service.statistics;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.common.core.domain.entity.SysUser;
import org.biosino.lf.pds.common.utils.uuid.IdUtils;
import org.biosino.lf.pds.system.domain.statistics.StatisticsUser;
import org.biosino.lf.pds.system.mapper.statistics.StatisticsUserMapper;
import org.biosino.lf.pds.system.service.ISysUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 用户统计服务
 *
 * <AUTHOR>
 * @date 2025/9/3
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsUserService {

    private final ISysUserService sysUserService;
    private final StatisticsUserMapper statisticsUserMapper;

    /**
     * 全量统计
     */
    public void calculateAll() {
        // 清空所有统计数据
        statisticsUserMapper.delete(null);

        // 找到最老的用户数据
        SysUser oldestUser = sysUserService.getOne(
                Wrappers.<SysUser>lambdaQuery()
                        .orderByAsc(SysUser::getCreateTime)
                        .last("LIMIT 1")
        );

        if (oldestUser != null) {
            Date date = DateUtil.beginOfMonth(oldestUser.getCreateTime());
            Date now = new Date();

            // 从最老的用户创建时间开始，逐月统计到当前月份
            while (date.before(now)) {
                calculateByMonth(date);
                date = DateUtil.offsetMonth(date, 1);
            }
        }

        log.info("用户统计全量计算完成");
    }

    /**
     * 计算当月的用户统计
     */
    public void calculateByMonth() {
        calculateByMonth(new Date());
    }

    /**
     * 按指定月份计算用户统计
     *
     * @param date 指定日期，如果为null则使用当前日期
     */
    @Transactional(rollbackFor = Exception.class)
    public void calculateByMonth(Date date) {
        if (date == null) {
            date = new Date();
        }

        // 获取当前日期的结束时间
        DateTime endOfMonth = DateUtil.endOfMonth(date);
        DateTime beginOfMonth = DateUtil.beginOfMonth(date);

        // 删除记录
        int year = DateUtil.year(date);
        int month = DateUtil.month(date) + 1;

        log.info("开始计算用户统计: {}-{}", year, month);

        // 统计这个月底之前的用户总数
        long totalCount = sysUserService.count(
                Wrappers.<SysUser>lambdaQuery()
                        .le(SysUser::getCreateTime, endOfMonth)
        );

        // 统计这个月内新增的用户数量
        long newCount = sysUserService.count(
                Wrappers.<SysUser>lambdaQuery()
                        .ge(SysUser::getCreateTime, beginOfMonth)
                        .le(SysUser::getCreateTime, endOfMonth)
        );

        StatisticsUser item = new StatisticsUser();
        item.setId(IdUtils.getSnowflakeNextId());
        item.setYear(year);
        item.setMonth(month);
        item.setTotal(totalCount);
        item.setTotalGrowth(newCount);

        // 删除原来的月份数据
        statisticsUserMapper.deleteByYearAndMonth(year, month);
        // 新增统计数据
        statisticsUserMapper.insert(item);

        log.info("用户统计完成：{}-{}, 总数: {}, 新增: {}", year, month, totalCount, newCount);
    }
}
